﻿#include "cachedatabase.h"
#include "paramstructdefine.h"
#include <QThread>
#include <QSqlQuery>
#include "configservice.h"
#include "log.h"
#include "dbserver.h"
#define  CACHEDB_PATH "/media/data/CacheDB"

/*************************************************
功能： 初始化i2缓存数据库
输入参数：
*************************************************************/
void CacheDataBase::initI2CacheDB()
{       
    QSqlQuery query(m_cacheSqliteDB);

    //从配置中获取I2主站信息
    QString ip = ConfigService::instance().getI2Set().at(0).strIP;
    QString port = QString::number(ConfigService::instance().getI2Set().at(0).nPort);
    QStringList table_list;
    table_list.append("I2_"+ip+"_"+port);
    //I2从站
    if ( !ConfigService::instance().getI2Set().at(1).strIP.isEmpty() )
    {
       QString ip = ConfigService::instance().getI2Set().at(1).strIP;
       QString port = QString::number(ConfigService::instance().getI2Set().at(1).nPort);
       table_list.append("I2_"+ip+"_"+port);
    }

    QStringListIterator strIterator(table_list);
    while (strIterator.hasNext())
    {
        query.exec(QString("CREATE TABLE '%1' ("
                       "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                       "TestPointID CHAR(40), "
                       " RecordTime CHAR(40))").arg(strIterator.next()));
        query.clear();
    }

}

/*************************************************
功能： 初始化104缓存数据库
输入参数：
*************************************************************/
void CacheDataBase::initIec104CacheDB(QString strIPAddress)
{   
    emit sigCreate104DataTable(strIPAddress);
}

CacheDataBase::CacheDataBase(QObject *parent) : QObject(parent)
{
    initCacheDB();

    connect(this, &CacheDataBase::sigAddI2Data, this, &CacheDataBase::OnAddI2Data);
    connect(this, &CacheDataBase::sigDelI2Data, this, &CacheDataBase::OnDelI2Data);
    connect(this, &CacheDataBase::sigCreate104DataTable, this, &CacheDataBase::OnCreate104DataTable);
    connect(this, &CacheDataBase::sigAdd104Data, this, &CacheDataBase::OnAdd104Data);
    connect(this, &CacheDataBase::sigDel104Data, this, &CacheDataBase::OnDel104Data);

    m_pThread = new QThread;
    moveToThread(m_pThread);
    m_pThread->start();
}

/*************************************************
功能： 数据从缓存库查询是否存在这条记录
输入参数：
        strPointID :-- 测点ID
        Recordtime：--时间

输出参数：
返回值：
        true -- 存在
        false -- 不存在
*************************************************************/
bool CacheDataBase::assertIfExistInCacheDB(const QString &table,
                          const QString &strPointID,
                          const QString &daqtime)
{
    bool bRet = false;
    QSqlQuery query(m_cacheSqliteDB);
    query.exec(QString("SELECT * FROM '%1' WHERE TestPointID='%2' AND RecordTime='%3'")
                                                           .arg(table)
                                                           .arg(strPointID)
                                                           .arg(daqtime));
    if (query.next())
    {
        bRet = true;
    }
    query.clear();
    return bRet;
}

void CacheDataBase::OnCreate104DataTable(QString strIPAddress)
{
    QSqlQuery query(m_cacheSqliteDB);

    QString table = "IEC104_"+strIPAddress+"_0";

    query.exec(QString("CREATE TABLE '%1' ("
                       "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                       "TestPointID CHAR(40), "
                       " RecordTime CHAR(40))").arg(table));
    query.clear();
}

void CacheDataBase::OnAdd104Data(QString strTestPointId, QString strRecordTime)
{
    QStringList tables = m_cacheSqliteDB.tables();

    //数据库中所有104的表
    for( int i=0; i<tables.count(); i++ )
    {
        if ((tables.at(i) != "main") && (tables.at(i) != "sqlite_sequence") && (tables.at(i).split('_').at(0)=="IEC104"))
        {
            addRecordToCacheDB(tables.at(i),strTestPointId,strRecordTime);

            PDS_SYS_TRACE_LOG("addRecordToCacheDB:     table:%s---TestPointId:%s---RecordTime:%s---",
                                tables.at(i).toLatin1().data(),
                                strTestPointId.toLatin1().data(),
                                strRecordTime.toLatin1().data());
        }
    }
}

void CacheDataBase::OnDel104Data(QString strTestPointId, QString strRecordTime, QString strIP)
{
    QStringList tables = m_cacheSqliteDB.tables();
    logInfo("#@CacheDataBase::delCacheDB curThread=") << QThread::currentThread() << this->thread();
    for( int i=0; i<tables.count(); i++ )
    {
        //数据库中对应104表
        if ((tables.at(i) != "sqlite_sequence") && (tables.at(i).split('_').at(0) == "IEC104") &&
                (tables.at(i).split('_').at(1) == strIP))
        {
            delRecordToCacheDB(tables.at(i),strTestPointId,strRecordTime);
        }
    }
}

void CacheDataBase::OnAddI2Data(QString strCagIP, quint32 uiPort, const QVector<DataNode> &dataVector)
{
    QString strTestPointId;
    QString strstrRecordTime;
    QString table = "I2_" + strCagIP + "_" + QString::number(uiPort);

    //表不存在创建
    if(!m_cacheSqliteDB.tables().contains(table))
    {
        QSqlQuery query(m_cacheSqliteDB);

        // 创建表
        if(!query.exec(QString("CREATE TABLE IF NOT EXISTS '%1' ("
                           "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                           "TestPointID CHAR(40), "
                           "RecordTime CHAR(40))").arg(table)))
        {
            logError("Failed to create table: ") << table << query.lastError().text();
        }

        // 创建索引以提高查询性能
        if(!query.exec(QString("CREATE INDEX IF NOT EXISTS idx_%1_testpoint ON '%1'(TestPointID)").arg(table)))
        {
            logError("Failed to create TestPointID index: ") << query.lastError().text();
        }

        if(!query.exec(QString("CREATE INDEX IF NOT EXISTS idx_%1_recordtime ON '%1'(RecordTime)").arg(table)))
        {
            logError("Failed to create RecordTime index: ") << query.lastError().text();
        }

        // 创建复合索引用于常见查询
        if(!query.exec(QString("CREATE INDEX IF NOT EXISTS idx_%1_composite ON '%1'(TestPointID, RecordTime)").arg(table)))
        {
            logError("Failed to create composite index: ") << query.lastError().text();
        }

        query.clear();
    }

    for(auto iter=dataVector.constBegin(); iter != dataVector.constEnd(); ++iter)
    {
        strstrRecordTime = iter->s_sNodeInfo.s_strSampleTime;
        for(DataAttribute attr : iter->s_vDataAttrs)
        {
            if(attr.s_strName == "InnerSensorID")
            {
               strTestPointId = attr.s_strValue;
               break;
            }
        }

        addRecordToCacheDB(table, strTestPointId, strstrRecordTime);

        PDS_SYS_TRACE_LOG("addRecordToCacheDB:     table:%s---TestPointId:%s---RecordTime:%s---",
                        table.toLatin1().data(),
                        strTestPointId.toLatin1().data(),
                        strstrRecordTime.toLatin1().data());
    }
}

void CacheDataBase::OnDelI2Data(QString strCagIP, quint32 uiPort, const QVector<DataNode> &dataVector)
{
    QString strTestPointId;
    QString strstrRecordTime;
    QString table = "I2_" + strCagIP + "_" + QString::number(uiPort);

    for(auto iter=dataVector.constBegin();iter != dataVector.constEnd();++iter)
    {
        strstrRecordTime = iter->s_sNodeInfo.s_strSampleTime;
        for(DataAttribute attr : iter->s_vDataAttrs)
        {
            if(attr.s_strName == "InnerSensorID")
            {
               strTestPointId = attr.s_strValue;
               break;
            }
        }

        delRecordToCacheDB(table,strTestPointId,strstrRecordTime);

        PDS_SYS_TRACE_LOG("delRecordToCacheDB:     table:%s---TestPointId:%s---RecordTime:%s---",
                        table.toLatin1().data(),
                        strTestPointId.toLatin1().data(),
                        strstrRecordTime.toLatin1().data());
    }
}

/*************************************************
功能： 数据写入缓存数据库
输入参数：
        strPointID :-- 测点ID
        Recordtime：--时间

输出参数：
返回值：
        true -- 成功
        false -- 失败
*************************************************************/
bool CacheDataBase::addRecordToCacheDB(const QString &table,
                         const QString &strPointID,
                         const QString &daqtime)
{
    bool bRet = false;
    if(assertIfExistInCacheDB(table,strPointID,daqtime))
    {
        return true;
    }

    QSqlQuery query(m_cacheSqliteDB);
    bRet = query.exec(QString("INSERT INTO '%1' (TestPointID, RecordTime) "
                   "VALUES ('%2', '%3')").arg(table)
                                         .arg(strPointID)
                                         .arg(daqtime));
    query.clear();
    return bRet;
}

/*************************************************
功能： 数据信息从缓存数据库删除
输入参数：
        strPointID :-- 测点ID
        Recordtime：--时间

输出参数：
返回值：
        true -- 成功
        false -- 失败
*************************************************************/
bool CacheDataBase::delRecordToCacheDB(const QString &table,
                        const QString &strPointID,
                        const QString &daqtime)
{
    bool bRet = false;
    QSqlQuery query(m_cacheSqliteDB);
    bRet = query.exec(QString("DELETE FROM '%1' WHERE "
                "TestPointID='%2' AND RecordTime='%3'").arg(table)
                                                       .arg(strPointID)
                                                       .arg(daqtime));
    query.clear();
    return bRet;
}

bool CacheDataBase::addI2DataToCacheDB(QString strCagIP, quint32 uiPort,\
                               const QVector<DataNode>& dataVector)
{
    emit sigAddI2Data(strCagIP,uiPort, dataVector);

    return true;
}

bool CacheDataBase::delI2DataFromCacheDB(QString strCagIP, quint32 uiPort, \
                               const QVector<DataNode>& dataVector)
{
    emit sigDelI2Data(strCagIP,uiPort, dataVector);

    return true;
}

bool CacheDataBase::add104DataToCacheDB(QString strTestPointId, QString strRecordTime)
{
    emit sigAdd104Data(strTestPointId, strRecordTime);
    return true;
}

bool CacheDataBase::del104DataFromCacheDB(QString strTestPointId, QString strRecordTime, QString strIP)
{
    emit sigDel104Data(strTestPointId, strRecordTime, strIP);
    return true;
}

CacheDataBase::~CacheDataBase()
{
    if(m_pThread)
    {
        if(m_pThread->isRunning())
        {
            m_pThread->exit();
            m_pThread->wait();
        }

        delete m_pThread;
        m_pThread = nullptr;
    }
}

bool CacheDataBase::initCacheDB()
{
    bool bInitRet = false;

    QString dirPath(CACHEDB_PATH);
    QDir dataBaseDir(dirPath);
    if (!dataBaseDir.exists())
    {
        logError("initCacheDB:creat");
        dataBaseDir.mkpath(dirPath);
    }
    //初始化缓存数据库
    m_cacheSqliteDB = QSqlDatabase::addDatabase("QSQLITE","CacheDataBase");
    QString strAbsoluteDBName = dirPath + QDir::separator()+ "CacheDataBase.db";
    m_cacheSqliteDB.setDatabaseName(strAbsoluteDBName);

    //
    bInitRet =  m_cacheSqliteDB.open();

    //初始化I2失败数据
    //initI2CacheDB();

    return bInitRet;
}

/************************************************
 * 功能：单例
 ************************************************/
CacheDataBase* CacheDataBase::instance()
{
    static CacheDataBase cadb;

    return &cadb;
}

bool CacheDataBase::getI2FailData(QList<PDSMPDATA> &failDataList, QString strCagIP, quint32 uiPort)
{
    logInfo("getI2FailData...");
    const QString tableName = "I2_" + strCagIP + "_" + QString::number(uiPort);

    bool bGetRet = m_cacheSqliteDB.tables().contains(tableName);
    if(bGetRet)
    {
        QString strQuery = QString("select TestPointID,RecordTime from '%1'").arg(tableName);
        QSqlQuery query(m_cacheSqliteDB);
        query.exec(strQuery);

        while(query.next())
        {
            DataRecordInfo stInfo;
            stInfo.strTestPointId = query.value(0).toString();
            stInfo.qdtRecordTime = query.value(1).toDateTime();

            PDSMPDATA pdData;
            bool ret = DBServer::instance().getPDSMPDataAtTimeMoment(stInfo.strTestPointId, stInfo.qdtRecordTime, pdData);
            logInfo("get I2 fail data: ") << ret << stInfo.strTestPointId <<stInfo.qdtRecordTime;
            failDataList.append(pdData);
        }
    }

    return bGetRet;
}

QList<DataRecordInfo> CacheDataBase::getI2FailDataInfo(QString strCagIP, quint32 uiPort)
{
    logInfo("get I2FailDataInfo start...");

    const QString tableName = "I2_" + strCagIP + "_" + QString::number(uiPort);

    QList<DataRecordInfo> failedDataInfo;

    bool bGetRet = m_cacheSqliteDB.tables().contains(tableName);
    if(bGetRet)
    {
        QString strQuery = QString("select TestPointID,RecordTime from '%1'").arg(tableName);
        QSqlQuery query(m_cacheSqliteDB);

        if(!query.exec(strQuery))
        {
            logError("getI2FailDataInfo query failed: ") << query.lastError().text();
            return failedDataInfo;
        }

        while(query.next())
        {
            DataRecordInfo stInfo;
            stInfo.strTestPointId = query.value(0).toString();
            stInfo.qdtRecordTime = query.value(1).toDateTime();

            failedDataInfo.append(std::move(stInfo));
        }
    }

    logInfo("get I2FailDataInfo end.failed data size: ") << failedDataInfo.size();

    return std::move(failedDataInfo);
}

QList<DataRecordInfo> CacheDataBase::getI2FailDataInfoPaged(QString strCagIP, quint32 uiPort, int pageSize, int pageIndex)
{
    logInfo("getI2FailDataInfoPaged start... pageSize:") << pageSize << "pageIndex:" << pageIndex;

    const QString tableName = "I2_" + strCagIP + "_" + QString::number(uiPort);
    QList<DataRecordInfo> failedDataInfo;

    bool bGetRet = m_cacheSqliteDB.tables().contains(tableName);
    if(bGetRet)
    {
        // 使用分页查询，按ID排序确保结果一致性
        int offset = pageIndex * pageSize;
        QString strQuery = QString("select TestPointID,RecordTime from '%1' ORDER BY id LIMIT %2 OFFSET %3")
                          .arg(tableName).arg(pageSize).arg(offset);
        QSqlQuery query(m_cacheSqliteDB);

        if(!query.exec(strQuery))
        {
            logError("getI2FailDataInfoPaged query failed: ") << query.lastError().text();
            return failedDataInfo;
        }

        while(query.next())
        {
            DataRecordInfo stInfo;
            stInfo.strTestPointId = query.value(0).toString();
            stInfo.qdtRecordTime = query.value(1).toDateTime();

            failedDataInfo.append(std::move(stInfo));
        }
    }

    logInfo("getI2FailDataInfoPaged end. page size: ") << failedDataInfo.size();
    return std::move(failedDataInfo);
}

int CacheDataBase::getI2FailDataCount(QString strCagIP, quint32 uiPort)
{
    const QString tableName = "I2_" + strCagIP + "_" + QString::number(uiPort);
    int count = 0;

    bool bGetRet = m_cacheSqliteDB.tables().contains(tableName);
    if(bGetRet)
    {
        QString strQuery = QString("select COUNT(*) from '%1'").arg(tableName);
        QSqlQuery query(m_cacheSqliteDB);

        if(query.exec(strQuery) && query.next())
        {
            count = query.value(0).toInt();
        }
        else
        {
            logError("getI2FailDataCount query failed: ") << query.lastError().text();
        }
    }

    logInfo("getI2FailDataCount: ") << count;
    return count;
}

bool CacheDataBase::delI2FailDataFromCacheDB(QString strCagIP, quint32 uiPort)
{
    const QString tableName = "I2_" + strCagIP + "_" + QString::number(uiPort);
    logInfo("delI2FailDataFromCacheDB: ") << tableName;
    if(m_cacheSqliteDB.tables().contains(tableName))
    {
        //删除表
        QString strSql = QString("DROP TABLE IF EXISTS '%1'").arg(tableName);
        QSqlQuery query(m_cacheSqliteDB);

        if(query.exec(strSql)) {
            logInfo("delI2FailDataFromCacheDB:drop table success: ") << tableName;
        } else {
            logError("delI2FailDataFromCacheDB:drop table failed: ") << tableName << query.lastError().text();
        }
    }
}

bool CacheDataBase::getFailData(OutwardProtocolType eType, QMap<QString, QList<PDSMPDATA> > &failDataMap)
{
    QString strProtocolType;
    bool bGetRet = false;

    switch (eType) {
    case I2:
        strProtocolType = "I2";
        break;
    case IEC104:
        strProtocolType = "IEC104";
        break;
    default:
        break;
    }

    if(strProtocolType.isEmpty())
    {
        return bGetRet;
    }

    QSqlQuery query(m_cacheSqliteDB);
    QMap<QString, QList<DataRecordInfo> > qmp4DataInfo;     //每张表对应的所有数据信息
    logInfo(m_cacheSqliteDB.tables()) << strProtocolType;

    for(QString strTableName : m_cacheSqliteDB.tables())
    {
        QStringList strNameList = strTableName.split('_');
        if(strTableName == "sqlite_sequence" || strNameList.size() != 3)
        {
            continue;
        }
        QString strTableType = strNameList.at(0);
        QString strIP = strNameList.at(1);
        //QString strPort = strNameList.at(2);

        if (strTableType == strProtocolType)
        {
            QString strQuery = QString("select TestPointID,RecordTime from '%1'")
                    .arg(strTableName);
            query.exec(strQuery);

            while(query.next())
            {
                DataRecordInfo stInfo;
                stInfo.strTestPointId = query.value(0).toString();
                stInfo.qdtRecordTime = query.value(1).toDateTime();
                qmp4DataInfo[strIP].append(stInfo);
            }
        }
        logInfo("strTableName") << strTableName;
    }

    for(auto iter = qmp4DataInfo.constBegin(); iter != qmp4DataInfo.constEnd(); ++iter)
    {
        for(const DataRecordInfo& stInfo : iter.value())
        {
            PDSMPDATA pdData;
            bool ret = DBServer::instance().getPDSMPDataAtTimeMoment(stInfo.strTestPointId, stInfo.qdtRecordTime, pdData);
            logInfo("qmp4DataInfo") << ret << iter.key() << stInfo.strTestPointId <<stInfo.qdtRecordTime;
            failDataMap[iter.key()].append(pdData);
        }

    }

    bGetRet = true;
    return bGetRet;
}



