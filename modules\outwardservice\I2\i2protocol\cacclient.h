﻿/*
* Copyright (c) 2018.1，南京华乘电气科技有限公司
* All rights reserved.
*
* CACClient.h
*
* 初始版本：1.0.0.0
* 作者：陈康
* 创建日期：2018年01月09日
* 摘要： 智能辅控终端webservice客户端，用来访问主机的服务

* 当前版本：1.0.0.0
*/
#ifndef CACCLIENT_H
#define CACCLIENT_H

#include <QThread>
#include <QMutex>
#include <QSemaphore>
#include <QVector>
#include <QMap>
#include <QSharedPointer>
#include "i2protocoldefine.h"
#include "paramstruct.h"
#include "cagproxy.h"
#include "i2accessinterface.h"
#include "ringbuffer.h"
#include "../versionmanager/versioninfodefine.h"
#include "pdied.h"
#include "cachedatabase.h"

class CACService;
class CACUpdateManager;

class CACClient : public QThread
{
    Q_OBJECT
public:
    friend class CACService;
    friend class CACUpdateManager;

    /************************************************
     * 功能：构造函数
     ************************************************/
    explicit CACClient(const QString &strConfigId, QObject *parent = 0);
    /************************************************
     * 功能：析构函数
     ************************************************/
    ~CACClient();

    //启动客户端
    void startClient();

    //停止客户端
    void stopClient();

    //停止线程
    void stopThread();

    //失败数据上报
    void failDataReport();

    /*************************************************
     函数名：
     输入参数：accessor -- 对外访问的接口的指针
     输出参数： NULL
     返回值： NULL
     功能： 设置对辅控终端其它模块的访问接口
     *************************************************************/
    void setCACAccessor( QSharedPointer<I2AccessInterface> accessor );

    /*************************************************
     函数名：
     输入参数： strIP -- 主站的IP
               uiPort -- 主站的端口号
     输出参数： NULL
     返回值： NULL
     功能： 设置主站通讯的IP和端口号
     *************************************************************/
    void setHostAddress( const QString &strIP, quint32 uiPort, const QString &strCac );
    inline QString getHostIp()const {return m_strCagIP;}

    /*************************************************
    函数名： uploadCACHeartbeatInfo
    输入参数： NULL
    输出参数： NULL
    返回值： 心跳信息处理结果及其他命令信息
    功能： 更新心跳信息
    *************************************************************/
    void uploadCACHeartbeatInfo();

    /*************************************************
     函数名：
     输入参数： dataNodes -- 需要周期上报的数据
     输出参数： NULL
     返回值： NULL
     功能： 添加需要周期上报的数据
     *************************************************************/
    void addUploadData(const QString &strPointID , const QString &strDataID);

    /*************************************************
    函数名： uploadCACData
    输入参数： eType --
    输出参数： NULL
    返回值： NULL
    功能： 上送数据
    *************************************************************/
    void uploadCACData( UploadDataType eType,
                        const QVector<DataNode> & nodes = QVector<DataNode>(),
                        const QString & strSn = "" );

    /*************************************************
    函数名： uploadCACConfig
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 上送配置信息
    *************************************************************/
    void uploadCACConfig( const QString & strSn = "");

    /*************************************************
    函数名： downloadCACLatestVersion
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 获取最新文件版本
    *************************************************************/
    void downloadCACLatestVersion( const NodeID &nodeId );

    /*************************************************
    函数名： downloadCACHistoryVersion
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 下载历史文件信息
    *************************************************************/
    void downloadCACHistoryVersion( const NodeID& nodeId );

    /*************************************************
    函数名： downloadCACUpdateFile
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 下载文件
    *************************************************************/
    void downloadCACUpdateFile(const I2VersionInfo &verInfo );

    /*************************************************
    函数名： reponseToCACCommand
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应配置命令（携带操作结果）
    *************************************************************/
    void reponseToCACCommand(const QVector<SetConfigParam> &setConfigParam);

    //上送云端下发命令执行状态
    void uploadCommandExecStatus(const CommandInfo &stCommandInfo);

    //获取命令执行信息
    static CommandInfo getCommandExecInfo(QString strCommandSn, QString strCommandObjID, I2CommandType strCommandType,
                                   CommandInfo::CommandExecRet eComandExecRet, QString strMessage);

    //向云端上送文件
    void uploadFile(const CommandInfo &stCommandInfo);

    //上送传感器固件更新结果
    //void uploadAduFirmUpdateStatus(QVariant aduID, bool bUpdateRet, QString strErrorMsg);

    //上送传感器固件更新结束消息
    //void uploadAduFirmUpdateEnd();

    /*************************************************
    函数名： isSlaveClientRun
    输入参数： NULL
    输出参数： NULL
    返回值：true/false
    功能：本客户端是否运行
    *************************************************************/
    bool isClientRunning() const;

signals:
    //启动client
    void sigStartClient();

    //停止client
    void sigStopClient();

    //上送失败缓存数据
    void sigFailDataReport();

    /************************************************
     * 功能：主站下发的pointID设置信息
     * 参数：strObjID 汇集节点ID
     *      pointIDMap -- point code 和 pointID的对应关系
     ************************************************/
    void sigSetPointID( QString strObjID, QMap<QString, QMap<QString, QString> > pointIDMap );

    /************************************************
     * 功能：修改配置信息的信号
     * 参数：configs -- 待修改的配置信息集合
     ************************************************/
    void sigModifyConfig( QMap<QString, QMap<QString, SetConfigParam> > configs );

    /************************************************
     * 功能：获取到最新版本信息的信号
     * 参数：verInfo -- 最新版本信息
     ************************************************/
    void sigLatestVersion( I2VersionInfo verInfo );

    /************************************************
     * 功能：获取到历史版本信息的信号
     * 参数：verInfos -- 历史版本信息的集合
     ************************************************/
    void sigHistoryVersions( QVector<I2VersionInfo> verInfos );

    /************************************************
     * 功能：获取到更新文件的信号
     * 参数：verInfo -- 文件的版本信息
     *      filePath -- 更新文件的保存路径
     ************************************************/
    void sigGetUpdateFile( I2VersionInfo verInfo, QString filePath );

    /************************************************
     * 功能：与主站的连接状态变化的信号
     * 参数：bConnected -- 与主站的连接情况
     ************************************************/
    void sigCagStateChanged( bool bConnected );

    /*************************************************
    输入参数： error -- 错误码
             info -- 错误信息
    功能： 发生错误的信号
    *************************************************/
    void sigError( I2Protocol::I2Error error, const QString &info );

protected:
    /*************************************************
    函数名： run
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 线程函数
    *************************************************************/
    virtual void run() override;
    
    //定时器事件处理
    void timerEvent(QTimerEvent *event) override;

private slots:
    //启动client
    void onStartClient();

    //停止client
    void onStopClient();

    //失败缓存数据上送
    void onFailDataReport();

    /*************************************************
    信号名： onCagStateChanged
    输入参数： bConnected 与主站的连接状态
    功能： 监测与主站的连接状态变化信号并进行处理
    *************************************************************/
    void onCagStateChanged( bool bConnected );

#ifdef I2_IED_MODE
    /*************************************************
    信号名： onReportData
    输入参数： PDSMPDATA：数据字段
    功能： 接受接入模块信号，上送数据
    *************************************************************/
    void onReportData(const PDSMPDATA &data);
#endif

private:
    struct DataIDInfo
    {
        QString s_strPointID;   //测试数据对应的测点ID
        QString s_strDataID;    //测试数据本身ID

        bool operator <( const DataIDInfo& other ) const
        {
            bool bRet;
            if( s_strPointID == other.s_strPointID )
            {
                bRet = s_strDataID < other.s_strDataID;
            }
            else
            {
                bRet = s_strPointID < other.s_strPointID;
            }
            return bRet;
        }

        bool operator ==( const DataIDInfo& other ) const
        {
            bool bRet = (s_strPointID == other.s_strPointID) &&
                    (s_strDataID == other.s_strDataID);

            return bRet;
        }
    };


    /**
     * @brief clearClient 清理客户端
     */
    void clearClient();  

    /**
     * @brief clearCacheDB 清理缓存数据库
     */
    void clearCacheDB();


    /*************************************************
    函数名：
    输入参数： pParam -- 访问主站指令的参数
              ePriority -- 指令执行的优先级
    输出参数： NULL
    返回值： NULL
    功能： 添加访问主站的指令
    *************************************************************/
    void addOperatorParam( QSharedPointer<ParamBase> pParam, TypePriority ePriority = TYPE_NORMAL );

    //主站下发控制指令的处理
    ResultInfo downloadCAGCtrl( const QString& strCtrl );

    /*************************************************
    函数名：
    输入参数： strType -- 类型信息的字符串描述
    输出参数： NULL
    返回值： 对应的命令枚举类型
    功能： 将命令的字符串描述转换为对应的枚举类型
    *************************************************************/
    I2CommandType coverStringTypeToEnum( const QString & strType );

    //更新数据的时间
    void updatePointEndTime( const QString & strPointId,
                             const QVector<DataNode> & vDatas );

    //填充周期数据的请求
    void fillPeriodDatas( QSharedPointer<UploadDataParam> pParam );

    //填充测点最新数据的请求
    void fillLatestDatas( QSharedPointer<UploadDataParam> pParam );

    //检查配置文件是否在队列中
    bool isConfigInQueue();

    //检查心跳包是否已在队列中
    bool isHeartbeatInQueue();

    //与主站通信失败时的本地缓存处理
    void failToHost( QSharedPointer<ParamBase> pParam, QString strErrorNO = QLatin1String(""));

    //与主站通信成功后本地的缓存处理
    void successToHost( QSharedPointer<ParamBase> pParam );

    //处理应答信息
    void handleResponseInfo(const ResponseInfo& resInfo, QSharedPointer<ParamBase> pParam );

    //处理指令列表
    void handleCommandList(const QVector<CommandInfo>& vCommands);

    //处理版本信息列表
    void handleVersionInfos( const QVector<I2VersionInfo>& vVersions, QSharedPointer<ParamBase> pParam );

    //处理更新文件
    void handleUpdateFiles(const QVector<UpdateFile>& vUpdateFiles, QSharedPointer<ParamBase> pParam );

    //处理数据召唤指令
    void handleGetNewData(const QVector<CommandInfo>& commandList);

    //处理数据重传指令
    void handleResendData( const QVector<CommandInfo>& commandList );

    //处理参数配置的指令
    void handleSetConfig( const QVector<CommandInfo>& commandList );

    //处理设置point_id的指令
    void handleSetPointID( const QVector<CommandInfo>& commandList );

    //处理OTA(固件更新)的指令
    void handleOTA( const QVector<CommandInfo>& commandList );

    //设置测点的point_id并过滤未设置point_id的数据
    bool handleNodePointID( DataNode& dataNode );

private:
    QString m_strConfigID;  //配置ID
    int m_iUploadHeartBeatTimerId {-1};     //心跳上送定时器ID
    int m_iUploadPeriodDataTimerId {-1};    //周期数据上送定时器ID
    QVector<QSharedPointer<ParamBase> > m_vOperateQueue;         //待远端访问的服务参数队列  普通方法加在队尾，紧急方法加在队首
    static const int MAX_OPERATE_QUEUE_SIZE = 2048;             //操作队列最大大小
    QMutex m_queueMutex;    //操作参数队列的互斥量
    QMutex m_cagMutex;      //主站访问的互斥信号量
    QSemaphore m_queueSem;  //参数队列的信号量
    volatile bool m_bRun;   //是否处理发送队列

    CAGProxy * m_pCagProxy; //主站服务的代理
    QString m_strCagIP;     //主站的IP
    quint32 m_uiPort;       //主站的端口号
    bool m_bCagConnected;   //与主站的通信状态
    QSharedPointer<I2AccessInterface> m_pAccessor;  //辅控终端其它模块接口的访问

    QMap<QString, I2TestPointInfo> m_pointInfoMap;    //pointid 和 测点信息的映射表
    QMap<QString, QString> m_pointEndTime;          //测点上一次获取的时间最大值映射表
    QMap<NodeID, QMap<QString, QString> > m_pointIdMap; //point_id的对应关系 key：测点标识，value:其下的数据名和point_id的对应

    QMutex m_PeriodDataMutex;                     //访问周期数据的互斥信号量
    RingBuffer<DataIDInfo> m_vPeriodDatas;        //需要周期上送的数据节点 NOTE：未发送成功的数据都缓存在该处
    RingBuffer<DataIDInfo> m_vPeriodBakDatas;     //需要周期上送的数据节点的备份，用于查询是否重复
    QVector<DataIDInfo> m_vSendingDatas;          //当前正在上送的周期数据节点
    RingBuffer<DataNode> m_vWarningDataNodes;           //本地缓存的需立即上送的数据节点
    QMap<QString, QVector<DataIDInfo> > m_callDataMap;  //主站历次召唤数据的缓冲 key:sn value:测试数据对应的ID
    RingBuffer<QSharedPointer<ParamBase> > m_configBuf; //缓存未成功发送的配置信息
    QVector<SetConfigParam>  m_setConfigList;
    QMap<UpdateFileNS::FileIDInfo, QSharedPointer<ParamBase> > m_updateFailMap;  //更新文件下载失败的队列

    RingBuffer<QSharedPointer<ParamBase> > m_commandExecStatusBuf; //缓存发送失败的命令执行信息
};

class CACUpdateManager : public QObject
{
    Q_OBJECT

    struct AduUpdateParameter
    {
        QStringList needUpdateAduList;              //待升级传感器列表
        QStringList updateSuccessfulAduList;        //升级成功的传感器列表

        bool getUpdateAduRet()                      //获取此次传感器升级结果(所有传感器都成功)
        {
            return needUpdateAduList == updateSuccessfulAduList;
        }
        void getFailedID(QStringList &list)         //获取升级失败的ID
        {
            foreach (QString strAduID, needUpdateAduList)
            {
                if(!updateSuccessfulAduList.contains(strAduID))
                {
                    list.append(strAduID);
                }
            }
        }
        void clear()
        {
            needUpdateAduList.clear();
            updateSuccessfulAduList.clear();
        }
    };
    enum UpdateDeviceType
    {
        UNKNOWN_DEVICE = -1,
        CAC_DEVICE = 0,
        SENSOR_DEVICE
    };

    struct CACUpdateInfo
    {
        UpdateDeviceType m_currentUpdateType = UNKNOWN_DEVICE;          //当前更新的设备类型
        CommandInfo m_currentHandleCommand;     //当前正在执行的OTA命令信息
        AduUpdateParameter m_currentAduUpdateInfo;     //当前正在更新的传感器信息

        void clear()
        {
            m_currentUpdateType = UNKNOWN_DEVICE;
            m_currentHandleCommand.clear();
            m_currentAduUpdateInfo.clear();
        }
    };

public:
    enum class UpdateErrorCode :quint16
    {
        COMMUNICATION_ERROR = 23,       //和平台通信异常
        DOWNLOAD_FILE_FAILED = 24,      //文件下载失败
        SAVE_FILE_FAILED = 25,          //保存文件失败
        FILE_CHECK_FAILED = 26,         //文件校验失败
        SAVE_INSTALL_INFO_FAILED = 27,   //保存安装信息失败
        SAME_VERSION = 28,                //版本相同
        INSTALL_TIMEOUT = 29              //安装固件超时
    };

public:
    //CACUpdateManager(CACClient* const pClient, QObject *parent=0);
    static CACUpdateManager& instance();

    //向ota命令列表尾部添加ota命令
    void addOTACommand(const QList<CommandInfo>& commandList);

    void delOTACommand(QString strCommandSN);

    //是否存在更新任务
    inline bool isExistUpdateTask() {return !m_OTACommandList.isEmpty();}

    //执行更新任务
    void doUpdateTask(const I2VersionInfo& verInfo);

    //处理更新失败
    void handleUpdateError(const I2VersionInfo& verInfo, UpdateErrorCode eErrorCode);

    //处理任务过期
    void handleTaskExpired(const I2VersionInfo& verInfo);

    //开始执行更新任务
    void startUpdateTask();

    //当前是否正在更新传感器
    bool isUpdateAdu();

    //处理传感器更新状态上送
    void handleAduFirmUpdateStatus(QVariant aduID, bool bUpdateRet, QString strErrorMsg);

    //处理传感器更新结束消息
    void handleAduFirmUpdateEnd();

private:
    //处理OTA命令列表头部命令
    void handleOTACommand();

    //OTA命令执行结束(会执行下一条任务)
    void OTACommandExecEnd();

    //OTA命令执行异常结束(任务执行停止)
    void OTACommandExecAbort();

    //移除ota命令列表第一条命令
    void removeFirstCommand(QString strCommandSN);

    //获取所有命令信息，用于打印
    QString getAllCommandInfo();

    //从任务文件中加载任务
    void loadOTATaskFromFile();

    //保存任务到任务文件
    void saveOTATaskToFile();

    //检查主机升级结果
    void checkCacUpdateRet();

    //上送任务执行状态
    void uploadTaskExecStatus(const I2VersionInfo& verInfo, _CommandInfo::CommandExecRet eComandExecRet, QString strMessage);

private:
    explicit CACUpdateManager(QObject *parent=0);
    CACUpdateManager(const CACUpdateManager& other);
    CACUpdateManager& operator=(const CACUpdateManager& other);

private:
    QList<CommandInfo> m_OTACommandList;    //需要执行的OTA命令列表
    bool m_bIsExecCommand;                  //当前是否正在执行OTA命令
    CACUpdateInfo m_currentUpdateInfo;      //当前更新信息记录
    QSharedPointer<CACClient> m_cacClient;  //当前任务所属主站客户端

    //CACClient* const m_cacClient;
    const QString m_OTATaskFilePath;        //OTA任务文件保存路径
    const QString m_OTATaskFileName;        //ota任务文件名称
    QMutex m_cacUpdateMutex;
};

#endif // CACCLIENT_H
