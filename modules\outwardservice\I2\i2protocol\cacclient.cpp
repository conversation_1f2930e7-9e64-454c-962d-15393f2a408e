#include "cacclient.h"
#include "paramstruct.h"
#include "versionmanager/versionmanager.h"
#include <QDebug>
#include "../../include/log/log.h"
#include "hstcommunication.h"
#include "log.h"
#include "configservice.h"
#ifdef I2_IED_MODE
#include "i2report.h"
#endif
#include "hostaccessor.h"
#include "configservice/outwardprotocolconfig/outwardprotocolconfig.h"
#include "configservice/outwardprotocolconfig/i2protocolconfigdefs.h"
#include "outcomminterface.h"
#include "dbserver.h"

using namespace I2Protocol;

#define MAX_PERIOD_DATA_SIZE 4096

CACClient::CACClient(const QString &strConfigId, QObject *parent):QThread(parent),
    m_strConfigID(strConfigId), m_bRun(false),
    m_pCagProxy(nullptr), m_bCagConnected( false ),
    m_vPeriodDatas(MAX_PERIOD_DATA_SIZE), m_configBuf(1)
{
    m_pCagProxy = new CAGProxy;

    connect(this, SIGNAL(sigCagStateChanged(bool)), this, SLOT(onCagStateChanged(bool)));

    connect(this, &CACClient::sigStartClient, this, &CACClient::onStartClient);
    connect(this, &CACClient::sigStopClient, this, &CACClient::onStopClient);

    connect(this, &CACClient::sigFailDataReport, this, &CACClient::onFailDataReport);
}

CACClient::~CACClient()
{
    if(isRunning())
    {
        stopThread();
        wait();
    }
    if(m_pCagProxy)
    {
        delete m_pCagProxy;
        m_pCagProxy = nullptr;
    }
}

void CACClient::startClient()
{
    emit sigStartClient();
}

void CACClient::stopClient()
{
    emit sigStopClient();
}

void CACClient::stopThread()
{
    m_bRun = false;
    m_queueSem.release();
}

void CACClient::failDataReport()
{
    emit sigFailDataReport();
}

void CACClient::run()
{
    m_bRun = true;

    while( m_bRun )
    {
        PDS_SYS_TRACE_LOG("m_vOperateQueue size is : %d %s",m_vOperateQueue.size(),m_strCagIP.toLatin1().data());
        m_queueSem.acquire();
        if( m_vOperateQueue.size() > 0 )
        {
            //获取队列头部的待发指令
            m_queueMutex.lock();
            QSharedPointer<ParamBase> pParam = m_vOperateQueue.first();
            m_vOperateQueue.pop_front();
            m_queueMutex.unlock();

            //请求主站服务
            QString strParamXml = pParam->outputXmlParam(  );
            QString strResponse = m_pCagProxy->remoteExe( pParam->operateType(), strParamXml );

            //------for test------
            PDS_SYS_TRACE_LOG("----%s:%d begin----",m_strCagIP.toLatin1().data(),m_uiPort);
            logTrace("CACClient::run-----request:\n") << strParamXml;
            if(pParam->operateType() != DOWNLOAD_FILE)
            {
                logTrace("CACClient::run-----Response:\n") << strResponse;
            }
//            if ( strResponse.contains("errorcode") )
//            {

//                PDS_SYS_TRACE_LOG("%s",strParamXml.toUtf8().data());
//                PDS_SYS_TRACE_LOG("%s",strResponse.toUtf8().data());
//            }
//            else
//            {
//                PDS_SYS_TRACE_LOG("%s",strParamXml.toUtf8().data());
//                PDS_SYS_TRACE_LOG("%s",strResponse.toUtf8().data());
//            }
            PDS_SYS_TRACE_LOG("----%s:%d end----",m_strCagIP.toLatin1().data(),m_uiPort);

            ResponseInfo repInfo = pParam->inputXmlResponse( strResponse );
            bool bRemoteResult = true;
            switch( repInfo.s_resultInfo.s_eResultCode )
            {
            case RESULT_SUCCESS:
            {
                //处理应答信息
                handleResponseInfo( repInfo, pParam );

                //对本地缓存数据进行处理
                successToHost( pParam );
            }
                break;
            case RESULT_FAIL://指令处理出错
            {                
                QString strErrorNO;  //获取详细错误码
                if(!repInfo.s_resultInfo.s_vErrorInfos.isEmpty())
                {
                    strErrorNO = repInfo.s_resultInfo.s_vErrorInfos.first().s_strErrorNO;
                }
                //与主站通信失败的处理
                failToHost( pParam, strErrorNO );

                //处理应答信息
                handleResponseInfo( repInfo, pParam );

                //打印出错信息
                for( int i = 0; i < repInfo.s_resultInfo.s_vErrorInfos.size(); ++i )
                {
                    PDS_SYS_WARNING_LOG("CACClient %s:%d --error code is :%s",m_strCagIP.toLatin1().data(),m_uiPort,repInfo.s_resultInfo.s_vErrorInfos.at(i).s_strErrorNO.toLatin1().data());
                }

                emit sigError(I2Protocol::ReplyError, QString("Master replay error info"));
            }
                break;
            case RESULT_FORMATERROR://与主站通信失败
            {
                if(strResponse.isEmpty())
                {
                    //与主站通信失败的处理
                    failToHost( pParam );

                    bRemoteResult = false;
                    PDS_SYS_TRACE_LOG("receive empty response: %s:%d",m_strCagIP.toLatin1().data(),m_uiPort);

                    if (!m_strCagIP.isEmpty())
                    {
                        emit sigError(I2Protocol::CommError, QStringLiteral("与主站通信失败"));
                    }
                }
                else
                {
                    //success
                    successToHost( pParam );
                }

            }
                break;
            default:
                break;
            }

            //与主站通信状态改变后发出信号
            if( bRemoteResult != m_bCagConnected )
            {
                m_bCagConnected = bRemoteResult;
                emit sigCagStateChanged( bRemoteResult );

                //---for test
                HSTCommunication::instance()->setHostConnectState(m_strCagIP, m_uiPort, bRemoteResult);
            }
        }
    }

    m_bRun = false;
}

void CACClient::timerEvent(QTimerEvent *event)
{
    const int iTimerId = event->timerId();
    if(iTimerId == m_iUploadHeartBeatTimerId)
    {
        uploadCACHeartbeatInfo();
    }
    else if(iTimerId == m_iUploadPeriodDataTimerId)
    {
        uploadCACData(UPLOAD_PERIOD);
    }
    else
    {

    }
}

void CACClient::onStartClient()
{
    logInfo("CACClient::onStartClient begin.configID: ") << m_strConfigID;

    QSharedPointer<OutwardProtocolConfig> spProtocolConfig = ConfigService::instance().getOutwardProtocolConfig();
    if( !isClientRunning() )
    {
        //配置状态置为启用中
        spProtocolConfig->setI2ProtocolConfigStatus(m_strConfigID, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_ENABLING);

        //清除历史数据
        clearClient();

        //加载配置
        const OutwardProtocolDefine::I2ProtocolConfig config = spProtocolConfig->getI2ProtocolConfig(m_strConfigID);
        setHostAddress(config.strServerIP, config.usPort, config.strUrl);

        //启动心跳上送定时器
        if(-1 != m_iUploadHeartBeatTimerId)
        {
            killTimer(m_iUploadHeartBeatTimerId);
        }
        if(config.nHeartInterval > 0)
        {
           m_iUploadHeartBeatTimerId = startTimer(config.nHeartInterval * 1000);
        }

        //启动周期数据上送定时器
        if(-1 != m_iUploadPeriodDataTimerId)
        {
            killTimer(m_iUploadPeriodDataTimerId);
        }
        if(config.nDataInterval > 0)
        {
            m_iUploadPeriodDataTimerId = startTimer(config.nDataInterval * 1000);
        }

        //启动线程
        start();

        //配置状态置为已启用
        spProtocolConfig->setI2ProtocolConfigStatus(m_strConfigID, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_ENABLED);

        //启动后上传配置
        uploadCACConfig();

        //启动cac更新服务
        CACUpdateManager::instance();

        //失败重传
        //onFailDataReport();

    }
    else
    {
        //配置状态置为已启用
        spProtocolConfig->setI2ProtocolConfigStatus(m_strConfigID, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_ENABLED);

        logWarnning("CACClient::startClient failed, client is running: ") << m_strConfigID;
    }

    logInfo("CACClient::onStartClient end.configID: ") << m_strConfigID;
}

void CACClient::onStopClient()
{
    logInfo("CACClient::onStopClient begin.configID: ") << m_strConfigID;

    QSharedPointer<OutwardProtocolConfig> spProtocolConfig = ConfigService::instance().getOutwardProtocolConfig();
    if(isClientRunning())
    {
        //配置状态置为停用中
        spProtocolConfig->setI2ProtocolConfigStatus(m_strConfigID, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_DISABLING);

        //停止定时器
        if(-1 != m_iUploadHeartBeatTimerId)
        {
            killTimer(m_iUploadHeartBeatTimerId);
            m_iUploadHeartBeatTimerId = -1;
        }
        if(-1 != m_iUploadPeriodDataTimerId)
        {
            killTimer(m_iUploadPeriodDataTimerId);
            m_iUploadPeriodDataTimerId = -1;
        }

        //停止处理线程
        stopThread();

        //清理缓存数据库
        clearCacheDB();

        //清理数据
        clearClient();

        //配置状态置为已停用
        spProtocolConfig->setI2ProtocolConfigStatus(m_strConfigID, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_DISABLED);
    }
    else
    {
        //配置状态置为已停用
        spProtocolConfig->setI2ProtocolConfigStatus(m_strConfigID, OutwardProtocolDefine::OutwardProtocolStatus::PROTOCOL_STATUS_DISABLED);
        logWarnning("CACClient::stopClient failed, client is not running: ") << m_strConfigID;
    }

    logInfo("CACClient::onStopClient end.configID: ") << m_strConfigID;
}

#if 0
void CACClient::onFailDataReport()
{
    logInfo(QString("CACClient::onFailDataReport begin,ip:%1,port:%2.").arg(m_strCagIP, QString::number(m_uiPort)));

    QList<PDSMPDATA> failDataList;
    CacheDataBase::instance()->getI2FailData(failDataList, m_strCagIP, m_uiPort);

    for(auto iter = failDataList.cbegin(); iter != failDataList.cend(); ++iter)
    {
        PDSMPDATA rptData;
        OutCommInterface::convertData(*iter, rptData);

        QVector<DataNode> dataNodeVector;
        DataReport::instance().getDataNodes(rptData, dataNodeVector);
        if(!dataNodeVector.isEmpty())
        {
            uploadCACData(UPLOAD_WARNING, dataNodeVector);
        }
        else
        {
            logWarnning("CACClient::onFailDataReport, dataNodeVector is empty.");
        }
    }

    logInfo(QString("CACClient::onFailDataReport end,ip:%1,port:%2.").arg(m_strCagIP, QString::number(m_uiPort)));
}
#endif
void CACClient::onFailDataReport()
{
    logInfo(QString("CACClient::onFailDataReport begin,ip:%1,port:%2.").arg(m_strCagIP, QString::number(m_uiPort)));

    // 首先获取总数量，判断是否需要分页处理
    int totalCount = CacheDataBase::instance()->getI2FailDataCount(m_strCagIP, m_uiPort);
    logInfo("Total fail data count: ") << totalCount;

    if(totalCount == 0)
    {
        logInfo("No fail data to report.");
        return;
    }

    const int BATCH_SIZE = 500; // 每批处理500条记录
    const int MAX_PROCESS_COUNT = 5000; // 单次最大处理数量限制

    int processCount = qMin(totalCount, MAX_PROCESS_COUNT);
    int pageCount = (processCount + BATCH_SIZE - 1) / BATCH_SIZE;

    if(totalCount > MAX_PROCESS_COUNT)
    {
        logWarnning("Too many fail data records (") << totalCount << "), limiting to " << MAX_PROCESS_COUNT;
    }

    for(int pageIndex = 0; pageIndex < pageCount; ++pageIndex)
    {
        QList<DataRecordInfo> failDataInfoList = CacheDataBase::instance()->getI2FailDataInfoPaged(m_strCagIP, m_uiPort, BATCH_SIZE, pageIndex);

        if(failDataInfoList.isEmpty())
        {
            logInfo("No more data in page: ") << pageIndex;
            break;
        }

        logInfo("Processing batch ") << pageIndex + 1 << "/" << pageCount << ", records: " << failDataInfoList.size();

        for(auto iter = failDataInfoList.cbegin(); iter != failDataInfoList.cend(); ++iter)
        {
            PDSMPDATA originPdData;
            bool ret = DBServer::instance().getPDSMPDataAtTimeMoment((*iter).strTestPointId, (*iter).qdtRecordTime, originPdData);

            if(!ret)
            {
                logWarnning("Failed to get PDSMP data: ") << (*iter).strTestPointId << (*iter).qdtRecordTime;
                continue;
            }

            PDSMPDATA rptData;
            OutCommInterface::convertData(originPdData, rptData);

            QVector<DataNode> dataNodeVector;
            DataReport::instance().getDataNodes(rptData, dataNodeVector);
            if(!dataNodeVector.isEmpty())
            {
                uploadCACData(UPLOAD_WARNING, dataNodeVector);
            }
            else
            {
                logWarnning("CACClient::onFailDataReport, dataNodeVector is empty for: ") << (*iter).strTestPointId;
            }
        }

        // 在批次之间添加短暂延迟，避免过度占用系统资源
        if(pageIndex < pageCount - 1)
        {
            QThread::msleep(10); // 10ms延迟
        }
    }

    logInfo(QString("CACClient::onFailDataReport end,ip:%1,port:%2, processed: %3/%4").arg(m_strCagIP, QString::number(m_uiPort), QString::number(qMin(processCount, totalCount)), QString::number(totalCount)));
}

//处理应答信息
void CACClient::handleResponseInfo( const ResponseInfo& resInfo, QSharedPointer<ParamBase> pParam )
{
    //处理命令列表
    handleCommandList( resInfo.s_vCommands );

    //处理版本信息列表
    handleVersionInfos( resInfo.s_vVersions, pParam );

    //处理远程更新文件下载列表
    handleUpdateFiles( resInfo.s_vUpdateFiles, pParam );
}

//处理指令列表
void CACClient::handleCommandList(const QVector<CommandInfo>& vCommands)
{
    if(vCommands.size()==0)
    {
        PDS_SYS_ERR_LOG("vCommands is null");
    }

    QVector<CommandInfo> vGetNewCmd;
    QVector<CommandInfo> vResendCmd;
    QVector<CommandInfo> vConfigCmd;
    QVector<CommandInfo> vSetcodeCmd;
    QVector<CommandInfo> vOTACmd;
    for( int iCmd = 0; iCmd < vCommands.size(); iCmd++ )
    {  
        switch( vCommands.at(iCmd).s_eType )
        {
        case CMD_TYPE_BEGIN:        //请求交互
        {
            uploadCACHeartbeatInfo();
        }
            break;
        case CMD_TYPE_GETCONFIG:    //获取配置
        {
            uploadCACConfig( vCommands.at(iCmd).s_strSN );
        }
            break;
        case CMD_TYPE_GETNEWDATA:   //数据召唤
        {
            //uploadCACData( UPLOAD_GETNEWDATA );
            vGetNewCmd.append( vCommands.at(iCmd) );
        }
            break;
        case CMD_TYPE_RESEND:       //数据重传
        {
            //uploadCACData( UPLOAD_RESEND );
            vResendCmd.append( vCommands.at(iCmd) );
        }
            break;
        case CMD_TYPE_END:          //结束交互
            // do nothing
            break;
        case CMD_TYPE_SETCONFIG:    //写配置交互
        {
            vConfigCmd.append( vCommands.at(iCmd) );
        }
            break;
        case CMD_TYPE_SETCODE:      //设置point_id
        {
            vSetcodeCmd.append( vCommands.at(iCmd) );
        }
            break;
        case CMD_TYPE_OTA:      //OTA(固件更新)
        {
            vOTACmd.append( vCommands.at(iCmd) );
        }
            break;
        default:
            break;
        }
    }

    if( !vGetNewCmd.isEmpty() )
    {
        handleGetNewData( vGetNewCmd );
    }
    if( !vResendCmd.isEmpty() )
    {
        handleResendData( vResendCmd );
    }
    if( !vConfigCmd.isEmpty() )
    {
        handleSetConfig( vConfigCmd );
    }
    if( !vSetcodeCmd.isEmpty() )
    {
        handleSetPointID( vSetcodeCmd );
    }
    if( !vOTACmd.isEmpty() )
    {
        handleOTA( vOTACmd );
    }
}

//处理数据召唤指令
void CACClient::handleGetNewData(const QVector<CommandInfo>& commandList)
{
    QList<QString> pointIDList;
    //解析测点的point_id
    foreach (CommandInfo cmdInfo, commandList)
    {
        foreach( ConfigValue cfgValue, cmdInfo.s_vConfig )
        {
            if( cfgValue.s_strName == INNER_SENSOR_ID_NAME )
            {
                pointIDList.append( cfgValue.s_strValue );
            }
        }
    }
    QVector<DataNode> tmpNodes;
    foreach( QString strPointID, pointIDList )
    {
        DataNode tmpNode;
        bool bRet = m_pAccessor->getSensorLatestData( strPointID, tmpNode );
        if( bRet )
        {
            tmpNodes.append( tmpNode );
        }
    }

    //生成发送报文
    QSharedPointer<UploadDataParam> pParam( new UploadDataParam( UPLOAD_GETNEWDATA ) );
    pParam->setHostSN( "" );
    pParam->setCACID( m_pAccessor->CACId() );
    pParam->addData( tmpNodes );

    addOperatorParam( pParam );
}

//处理数据重传指令
void CACClient::handleResendData( const QVector<CommandInfo>& commandList )
{
    QVector<DataNode> vNodes;
    foreach (CommandInfo cmdInfo, commandList)
    {
        QString strPointid;
        QString strTimeBegin;
        QString strTimeEnd;
        foreach( ConfigValue cfgValue, cmdInfo.s_vConfig )
        {
            if( cfgValue.s_strName == INNER_SENSOR_ID_NAME )
            {
                strPointid = cfgValue.s_strValue;
            }
            else if( cfgValue.s_strName == DATE_TIME_BEGIN )
            {
                strTimeBegin = cfgValue.s_strValue;
            }
            else if( cfgValue.s_strName == DATE_TIME_END )
            {
                strTimeEnd = cfgValue.s_strValue;
            }
            else if( cfgValue.s_strName == DATE_RANGE )
            {
                QStringList dates = cfgValue.s_strValue.split(DATE_RANGE_SPLITOR);
                if( dates.size() == 2 )
                {
                    strTimeBegin = dates.at(0);
                    strTimeEnd = dates.at(1);
                }
                else
                {
                    PDS_SYS_DEBUG_LOG("I2 resend date range in error format");
                }
            }
        }

        QVector<DataNode> tmpNodes;
        bool bRet = m_pAccessor->getSensorData( strPointid, strTimeBegin, strTimeEnd, tmpNodes );
        if( bRet )
        {
            vNodes +=  tmpNodes;
        }
    }
    //生成发送报文
    QSharedPointer<UploadDataParam> pParam( new UploadDataParam( UPLOAD_RESEND ) );
    pParam->setHostSN( "" );
    pParam->setCACID( m_pAccessor->CACId() );
    pParam->addData( vNodes );

    addOperatorParam( pParam );
}

//处理参数配置的指令
void CACClient::handleSetConfig(const QVector<CommandInfo> &commandList )
{
    PDS_SYS_TRACE_LOG("handleSetConfig");
    QMap<QString, QVector<CommandInfo> > objCmds;   //存储个汇集节点对应的命令
    //按照汇集节点对各个指令进行划分
    for( int i = 0; i < commandList.size(); i++ )
    {
        QString strId = commandList.at( i ).s_strObjID;
        objCmds[strId].append( commandList.at(i) );
    }

    QMap<QString, QMap<QString, SetConfigParam> > idMap;
    //处理各个汇集节点的指令
    for ( QMap<QString, QVector<CommandInfo> >::const_iterator iterObj = objCmds.cbegin();
          iterObj != objCmds.cend();
          ++iterObj )
    {
        QMap<QString, SetConfigParam> mapConfig;
        QString strId = iterObj.key();

        foreach( CommandInfo commandInfo, iterObj.value() )
        {
            SetConfigParam setConfig;
            QString strInnerSensorID;
            //将命令的属性集合转换为参数属性集合
            for (QVector<ConfigValue>::const_iterator iterAction = commandInfo.s_vConfig.cbegin();
                 iterAction != commandInfo.s_vConfig.cend();
                 ++iterAction)
            {
                PDS_SYS_TRACE_LOG("Para :%s,%s", iterAction->s_strName.toLatin1().data(),iterAction->s_strValue.toLatin1().data());
                setConfig.s_strSN = commandInfo.s_strSN;          //命令号
                if (iterAction->s_strName == INNER_SENSOR_ID_NAME)//传感器的内部ID属性变量
                {
                    strInnerSensorID = iterAction->s_strValue;
                }
                else
                {
                    setConfig.s_vValues.append(*iterAction);
                }
            }

            //如果没有传感器的InnerID，认为是辅控/汇集节点的配置
            SetConfigParam& config = strInnerSensorID.isEmpty() ? mapConfig[strId] : mapConfig[strInnerSensorID];
            config.s_strId = strInnerSensorID.isEmpty() ? strId : strInnerSensorID;
            config.s_strSN = commandInfo.s_strSN;
            config.s_strType ="SETCONFIG";
            for (QVector<ConfigValue>::iterator iter = setConfig.s_vValues.begin();
                 iter != setConfig.s_vValues.end();
                 ++iter)
            {
                config.s_vValues.push_back(*iter);
            }
        }
        idMap[strId] = mapConfig;
    }

    //解决卡顿问题
    //QVector<SetConfigParam>  m_setConfigList;
    for (QMap<QString, QMap<QString, SetConfigParam> >::iterator iter = idMap.begin();
         iter != idMap.end();
         ++iter)
    {
        QMap<QString, SetConfigParam>& setConfig = iter.value();
        for (QMap<QString, SetConfigParam>::iterator iterParam = setConfig.begin();
             iterParam != setConfig.end();
             ++iterParam)
        {
                //QString strPoindCode = iterParam.key();

                const SetConfigParam &config = iterParam.value();
                qDebug()<<"------------------------------";
                qDebug()<<config.s_strSN;
                qDebug()<<config.s_strId;
                qDebug()<<"------------------------------";
                SetConfigParam configTmp = config;
                bool bFlag = true;
                if(configTmp.s_strId == ConfigService::instance().auxCtrlTerminalInfo().strID ||configTmp.s_strId == ConfigService::instance().getIedRtuID())
                {
                    //辅控节点和汇集节点不做参数有效性判断
                    bFlag = true;
                }
                else
                {
                    //判断测点参数是否支持
                    QVector<ConfigAttribute> vConfigAttrs = DataReport::instance().getAllNodePara().value(configTmp.s_strId);
                    foreach(ConfigValue stConfigValue, configTmp.s_vValues)
                    {
                        PDS_SYS_TRACE_LOG("Para :%s,%s", stConfigValue.s_strName.toLatin1().data(),stConfigValue.s_strValue.toLatin1().data());
                        QVector<ConfigAttribute>::iterator iter = vConfigAttrs.begin();
                        while (iter != vConfigAttrs.end())
                        {
                            if(stConfigValue.s_strName == iter->s_strName)
                            {
                                break;
                            }
                            iter++;
                        }
                        if(iter == vConfigAttrs.end())
                        {
                            PDS_SYS_ERR_LOG("Para Invalid:%s,%s", stConfigValue.s_strName.toLatin1().data(),stConfigValue.s_strValue.toLatin1().data());
                            bFlag = false;
                            break;
                        }

                    }
                }
                //logInfo(QString("SetConfigParam:%1").arg(configTmp.s_strSN);
                if(bFlag)
                {
                    configTmp.s_strResults = "true";
                }
                else
                {
                    configTmp.s_strResults = "false";
                    idMap.remove(configTmp.s_strId);        //存在不支持的参数，直接删除
                }
                m_setConfigList.append(configTmp);
        }
    }
    //发送信号
    emit sigModifyConfig( idMap );
}

//处理设置point_id的指令
void CACClient::handleSetPointID(const QVector<CommandInfo> &commandList )
{
    QMap<QString, QVector<CommandInfo> > objCmds;   //存储个汇集节点对应的命令
    //按照汇集节点对各个指令进行划分
    for( int i = 0; i < commandList.size(); i++ )
    {
        QString strId = commandList.at( i ).s_strObjID;
        objCmds[strId].append( commandList.at(i) );
    }

    //处理各个汇集节点的指令
    for ( QMap<QString, QVector<CommandInfo> >::const_iterator iterObj = objCmds.cbegin();
          iterObj != objCmds.cend();
          ++iterObj )
    {
        QMap<QString, QMap<QString, QString> > idMap;//key:测点的code value:测点的名和point_id的map
        QString strId = iterObj.key();

        foreach( CommandInfo commandInfo, iterObj.value() )
        {
            QString strSensorId;
            QString strInnerId;
            QMap<QString, QString> pointMap;
            //将命令的属性集合转化为point_id对应的集合
            for (QVector<ConfigValue>::const_iterator iterAction = commandInfo.s_vConfig.cbegin();
                 iterAction != commandInfo.s_vConfig.cend();
                 ++iterAction)
            {
                QString strName = iterAction->s_strName;
                if( strName == SENSOR_ID_NAME )//传感器的主站ID
                {
                    strSensorId = iterAction->s_strValue;
                }
                else if( strName == INNER_SENSOR_ID_NAME )//传感器的内部ID
                {
                    strInnerId = iterAction->s_strValue;
                }
                else
                {
                    pointMap[strName] = iterAction->s_strValue;
                }
            }

            if (!strSensorId.isEmpty() && !strInnerId.isEmpty())
            {
                if (!idMap.contains(strInnerId))
                {
                    pointMap[strInnerId] = strSensorId;
                    idMap[strInnerId] = pointMap;
                }
                else
                {
                    for (QMap<QString, QString>::iterator iter = pointMap.begin();
                         iter != pointMap.end();
                         ++iter)
                    {
                        idMap[strInnerId].insert(iter.key(), iter.value());
                    }
                }
            }
        }
        PDS_SYS_DEBUG_LOG("CACClient::handleSetPointID objID : strId = %s", strId.toLatin1().data());
        emit sigSetPointID( strId, idMap );
        QList<QString> pointCodes = idMap.keys();
        foreach( QString strCode, pointCodes )
        {
            NodeID nodeId;
            nodeId.s_strObjId = strId;
            nodeId.s_strSensorId = strCode;
            m_pointIdMap[nodeId] = idMap.value( strCode );
        }
    }
}

void CACClient::handleOTA(const QVector<CommandInfo> &commandList)
{
    logInfo("CACClient::handleOTA...");
    QList<CommandInfo> otaCommandList = commandList.toList();
    //添加主站ip信息
    for(auto iter = otaCommandList.begin(); iter != otaCommandList.end(); ++iter)
    {
        (*iter).strStationIP = getHostIp();
    }

    CACUpdateManager::instance().addOTACommand(otaCommandList);
    //m_updateManager->addOTACommand(commandList.toList());
}

//处理版本信息列表
void CACClient::handleVersionInfos( const QVector<I2VersionInfo>& vVersions, QSharedPointer<ParamBase> pParam )
{
    QVector<I2VersionInfo> historyVers;
    //如果是获取历史版本，先清理现有的无效版本
    if( pParam->operateType() == DOWNLOAD_HISTORY_VERSION )
    {
        QSharedPointer<DownloadHistoryVersionParam> historyParam = qSharedPointerDynamicCast<DownloadHistoryVersionParam>(pParam);
        if( !historyParam.isNull() )
        {
            NodeID oriID = historyParam->nodeID();
            UpdateFileNS::NodeID tmpID;
            tmpID.s_strObjId = oriID.s_strObjId;
            tmpID.s_strSensorId = oriID.s_strSensorId;
            //VersionManager::instance()->clearInvalidVersion( tmpID );
        }
    }

    //处理版本信息，并和本地的信息统一
    for( int iVer = 0; iVer < vVersions.size(); iVer++ )
    {
        I2VersionInfo tmpVer = vVersions.at(iVer);
        //TODO 后继逻辑重新梳理
        if( !VersionManager::instance()->getInstallFileInfo( tmpVer.s_sFileIDInfo, tmpVer.s_sFileInfo ) )
        {
            PDS_SYS_TRACE_LOG("new version info: objid is %s, sensor id is %s " , tmpVer.s_sFileIDInfo.s_strObjId.toLatin1().data() , tmpVer.s_sFileIDInfo.s_strSensorId.toLatin1().data());
            tmpVer.s_sFileInfo.s_uiNextBlockNo = 1;
        }
        else
        {
            PDS_SYS_TRACE_LOG("the version info is already exists: objid is %s,  sensor id is %s ", tmpVer.s_sFileIDInfo.s_strObjId.toLatin1().data() , tmpVer.s_sFileIDInfo.s_strSensorId.toLatin1().data());
            tmpVer.s_sFileInfo.s_strReleaseTime = vVersions.at(iVer).s_sFileInfo.s_strReleaseTime;
        }
        //将文件信息保存到本地
        VersionManager::instance()->updateVersionInfo( tmpVer.s_sFileIDInfo,
                                                       tmpVer.s_sFileInfo.s_strFileName,
                                                       tmpVer.s_sFileInfo.s_uiTotalSize,
                                                       tmpVer.s_sFileInfo.s_strReleaseTime );

        if( pParam->operateType() == DOWNLOAD_HISTORY_VERSION )
        {
            historyVers.append( tmpVer );
        }
        else
        {
            emit sigLatestVersion( tmpVer );
            //如果该版本安装包曾经下载失败，则重新下载
            if( m_updateFailMap.contains( tmpVer.s_sFileIDInfo ) )
            {
                addOperatorParam( m_updateFailMap[tmpVer.s_sFileIDInfo] );
                m_updateFailMap.remove( tmpVer.s_sFileIDInfo );
            }
        }
    }

    if( !historyVers.isEmpty() )
    {
        sigHistoryVersions( historyVers );
    }
}

//处理更新文件
void CACClient::handleUpdateFiles( const QVector<UpdateFile>& vUpdateFiles, QSharedPointer<ParamBase> pParam )
{
    QSharedPointer<DownloadFileParam> downParam = qSharedPointerDynamicCast<DownloadFileParam>(pParam);
    if( !downParam.isNull() )
    {
        I2VersionInfo verInfo = downParam->getVersionInfo();
        for( int iFile = 0; iFile < vUpdateFiles.size(); iFile++ )
        {
            if( !vUpdateFiles.at(iFile).s_blockContent.isEmpty() )
            {
                bool bFileComplete = false;  //下载是否完成
                if( VersionManager::instance()->saveFileContent(
                            verInfo.s_sFileIDInfo, vUpdateFiles.at(iFile).s_blockContent, verInfo.s_sFileInfo.s_uiNextBlockNo, bFileComplete) )
                {
                    verInfo.s_sFileInfo.s_uiNextBlockNo++;
                    //判断下载是否完成
                    if(!bFileComplete)
                    {
                        PDS_SYS_TRACE_LOG("download not finished, already download %d, total size is %d." , ( verInfo.s_sFileInfo.s_uiNextBlockNo - 1 )*verInfo.s_uiBlockSize
                                 , verInfo.s_sFileInfo.s_uiTotalSize);
                        //继续下载下一包数据
                        downParam->setVersionInfo( verInfo );
                        addOperatorParam( downParam );
                    }
                    else
                    {                      
                        //执行固件更新
                        CACUpdateManager::instance().doUpdateTask(verInfo);
                    }
                }
                else
                {
                    //本地保存失败
                    PDS_SYS_ERR_LOG("save file failed");
                    verInfo.s_sFileInfo.s_uiNextBlockNo = 0;

                    CACUpdateManager::instance().handleUpdateError(verInfo, CACUpdateManager::UpdateErrorCode::SAVE_FILE_FAILED);
                }
            }
            else
            {
                //下载失败
                PDS_SYS_ERR_LOG("download file failed");
                verInfo.s_sFileInfo.s_uiNextBlockNo = 0;

                CACUpdateManager::instance().handleUpdateError(verInfo, CACUpdateManager::UpdateErrorCode::DOWNLOAD_FILE_FAILED);
            }

            //上报更新文件下载情况
            emit sigGetUpdateFile( verInfo, "" );
        }
    }
    else
    {
        //unreachable branch
        Q_ASSERT( downParam.isNull() );
    }
}

//与主站通信失败时的本地缓存处理
void CACClient::failToHost(QSharedPointer<ParamBase> pParam, QString strErrorNO )
{
    if( pParam->operateType() == UPLOAD_DATA )
    {
        QSharedPointer<UploadDataParam> updateParam = qSharedPointerDynamicCast<UploadDataParam>(pParam);
        if( !updateParam.isNull() )
        {
            switch( updateParam->uploadType() )
            {
            case UPLOAD_RESEND:
                //TODO 主站要求重发的请求，发送失败不做处理，主站可以再次发送重发请求
                break;
            case UPLOAD_GETNEWDATA:
                //TODO 主站主动请求的数据，发送失败不做处理，主站可以发送重发请求
                break;
            case UPLOAD_PERIOD:
            {
                //将未发送成功的周期数据移回周期数据缓冲区
                RingBuffer<DataIDInfo> tmpBuf( MAX_PERIOD_DATA_SIZE );
                tmpBuf.append( m_vSendingDatas.toList() );
                QMutexLocker locker( &m_PeriodDataMutex );
                QList<DataIDInfo> oriList = m_vPeriodDatas.takeAll();
                //过滤重复的数据ID
                foreach( DataIDInfo idInfo, oriList )
                {
                    if( !tmpBuf.contains( idInfo ) )
                    {
                        tmpBuf.append( idInfo );
                    }
                }

                m_vPeriodDatas.append( tmpBuf.takeAll() );
                m_vSendingDatas.clear();
            }
                break;
            case UPLOAD_WARNING:
            {
                QVector<DataNode> toAddDataNode;
                for(const DataNode& node : updateParam->uploadDatas())
                {
                    if(!m_vWarningDataNodes.contains(node))
                    {
                        toAddDataNode.append(node);
                    }
                }
                if(!toAddDataNode.isEmpty())
                {
                    m_vWarningDataNodes.append(toAddDataNode.toList());
                }
            }
                break;
            default:
                break;
            }
        }
        else
        {
            PDS_SYS_DEBUG_LOG("dynamic cast false");
        }
    }
    else if( pParam->operateType() == UPLOAD_CONFIG )
    {
        m_configBuf.append( pParam );
    }
    else if( pParam->operateType() == DOWNLOAD_FILE )
    {
        //文件获取失败，先缓存相应命令
        //QSharedPointer<DownloadFileParam> updateParam = qSharedPointerDynamicCast<DownloadFileParam>(pParam);
        //m_updateFailMap[updateParam->getVersionInfo().s_sFileIDInfo] = updateParam;

        QSharedPointer<DownloadFileParam> downParam = qSharedPointerDynamicCast<DownloadFileParam>(pParam);
        if( downParam.isNull() )
        {
            return;
        }
        if(strErrorNO.isEmpty())
        {
            CACUpdateManager::instance().handleUpdateError(downParam->getVersionInfo(), CACUpdateManager::UpdateErrorCode::COMMUNICATION_ERROR);
        }
        else
        {
            CACUpdateManager::instance().handleTaskExpired(downParam->getVersionInfo());
        }
    }
    else if( pParam->operateType() == UPLOAD_COMMAND_EXECUTE_STATUS )
    {
        if(strErrorNO.isEmpty())
        {
            m_commandExecStatusBuf.append( pParam );
        }
    }
    else
    {
        PDS_SYS_WARNING_LOG("no response :%s", m_strCagIP.toLatin1().data());
    }
}

//与主站通信成功后本地的缓存处理
void CACClient::successToHost(QSharedPointer<ParamBase> pParam )
{
    PDS_SYS_TRACE_LOG("successToHost %s %d", m_strCagIP.toLatin1().data(), m_uiPort);

    if( pParam->operateType() == UPLOAD_DATA )
    {
        QSharedPointer<UploadDataParam> updateParam = qSharedPointerDynamicCast<UploadDataParam>(pParam);
        if( !updateParam.isNull() )
        {

            switch( updateParam->uploadType() )
            {
            case UPLOAD_PERIOD:
            {
                //清空周期上送的缓存
                m_vSendingDatas.clear();
            }
                break;
            case UPLOAD_WARNING:
            {
                if(!m_vWarningDataNodes.isEmpty())
                {
                    //清理缓存中成功上送的数据节点
                    for(const DataNode& node : updateParam->uploadDatas())
                    {
                        if(m_vWarningDataNodes.contains(node))
                        {
                            m_vWarningDataNodes.removeOne(node);
                        }
                    }
                }
                //从数据库清除I2上送记录
                CacheDataBase::instance()->delI2DataFromCacheDB(m_strCagIP, m_uiPort, updateParam->uploadDatas());
            }
                break;
            default:
                break;
            }
        }
    }
    else if( pParam->operateType() == UPLOAD_CONFIG )
    {
        //上报缓存的配置信息
        if( !m_configBuf.isEmpty() )
        {
            addOperatorParam( m_configBuf.takeFirst() );
           PDS_SYS_DEBUG_LOG("CACClient::run : send config info in buffer");
        }
    }
    else
    {
        //nothing to do
    }
}

void CACClient::onCagStateChanged( bool bConnected )
{
    if( bConnected )
    {
//        logInfo(QString("--onCagStateChanged--%1 %2").arg(m_strCagIP).arg(m_uiPort));
        PDS_SYS_INFO_LOG("--onCagStateChanged--%s %d",m_strCagIP.toLatin1().data(),m_uiPort);
        //上报未发送成功的配置信息
        if( !m_configBuf.isEmpty() )
        {
            addOperatorParam( m_configBuf.takeFirst() );
            PDS_SYS_WARNING_LOG( "CACClient::onCagStateChanged : resend config info" );
        }
        //发送本地缓存的立即上报的数据
        if( !m_vWarningDataNodes.isEmpty() )
        {
            QList<DataNode> originalDataNodeList = m_vWarningDataNodes.toList();

            const int chunkSize = 5;    //单次上送数量控制（暂定）
            for (int i = 0; i < originalDataNodeList.size(); i += chunkSize)
            {
                QList<DataNode> uploadList = originalDataNodeList.mid(i, qMin(chunkSize, originalDataNodeList.size() - i));
                uploadCACData( UPLOAD_WARNING, uploadList.toVector() );
            }
        }

        //发送本地未发送成功的命令执行信息
        if( !m_commandExecStatusBuf.isEmpty() )
        {
            for(int i = 0, bufSize = m_commandExecStatusBuf.size(); i < bufSize; ++i)
            {
                addOperatorParam(m_commandExecStatusBuf.at(i));
            }
            m_commandExecStatusBuf.clear();
        }

        //继续更新任务(如果有)
        if(CACUpdateManager::instance().isExistUpdateTask())
        {
            CACUpdateManager::instance().startUpdateTask();
        }

        //发送本地缓存的周期上报的数据 TODO 是否需要立即补发？还是等数据上报周期到了再发送？
    }
}

/*************************************************
函数名：
输入参数： pParam -- 访问主站指令的参数
          ePriority -- 指令执行的优先级
输出参数： NULL
返回值： NULL
功能： 添加访问主站的指令
*************************************************************/
void CACClient::addOperatorParam( QSharedPointer<ParamBase> pParam, TypePriority ePriority )
{
    m_queueMutex.lock();
    PDS_SYS_TRACE_LOG("enter addOperatorParam");

    if( pParam->operateType() == UPLOAD_DATA )
    {
        QSharedPointer<UploadDataParam> updateParam = qSharedPointerDynamicCast<UploadDataParam>(pParam);
        if( !updateParam.isNull() )
        {
            switch( updateParam->uploadType() )
            {
            case UPLOAD_WARNING:
            {
                //I2上送记录保存到缓存数据库
                CacheDataBase::instance()->addI2DataToCacheDB(m_strCagIP, m_uiPort, updateParam->uploadDatas());
            }
                break;
            default:
                break;
            }
        }
        else
        {
            PDS_SYS_DEBUG_LOG("dynamic cast false");
        }
    }

    switch( ePriority )
    {
    case TYPE_NORMAL:
        m_vOperateQueue.push_back( pParam );
        break;
    case TYPE_URGENT:
        m_vOperateQueue.push_front( pParam );
        break;
    default:
        break;
    }

    m_queueMutex.unlock();
    m_queueSem.release();
}

//主站下发控制指令的处理
ResultInfo CACClient::downloadCAGCtrl( const QString& strCtrl )
{
    PDS_SYS_TRACE_LOG("rcv ctrl info : %s", strCtrl.toLatin1().data());
    ResultInfo resultInfo;

    QVector<CommandInfo> vcommands = ParamBase::parseRequestContent( strCtrl );
    //处理命令列表
    handleCommandList( vcommands );

    resultInfo.s_eResultCode = RESULT_SUCCESS;
    return resultInfo;
}

I2CommandType CACClient::coverStringTypeToEnum( const QString & strType )
{
    I2CommandType eType = CMD_TYPE_NONE;

    if( strType == "BEGIN" )
    {
        eType = CMD_TYPE_BEGIN;
    }
    else if( strType == "END" )
    {
        eType = CMD_TYPE_END;
    }
    else if( strType == "GETCONFIG" )
    {
        eType = CMD_TYPE_GETCONFIG;
    }
    else if( strType == "SETCONFIG" )
    {
        eType = CMD_TYPE_SETCONFIG;
    }
    else if( strType == "GETNEWDATA" )
    {
        eType = CMD_TYPE_GETNEWDATA;
    }
    else if( strType == "RESEND" )
    {
        eType = CMD_TYPE_RESEND;
    }
    else if( strType == "SETCODE" )
    {
        eType = CMD_TYPE_SETCODE;
    }
    else
    {
        eType = CMD_TYPE_NONE;
    }

    return eType;
}

/*************************************************
 函数名：
 输入参数：accessor -- 对外访问的接口的指针
 输出参数： NULL
 返回值： NULL
 功能： 设置对辅控终端其它模块的访问接口
 *************************************************************/
void CACClient::setCACAccessor( QSharedPointer<I2AccessInterface> accessor )
{
    m_pAccessor = accessor;
}

//void CACClient::startCACUpdateServer()
//{
//    logInfo("CACClient::startCACUpdateServer...");
//    //m_updateManager = QSharedPointer<CACUpdateManager>(new CACUpdateManager(this));
//}

/*************************************************
 函数名：
 输入参数： strIP -- 主站的IP
           uiPort -- 主站的端口号
 输出参数： NULL
 返回值： NULL
 功能： 设置主站通讯的IP和端口号
 *************************************************************/
void CACClient::setHostAddress(const QString &strIP, quint32 uiPort , const QString &strCac)
{
    if( NULL != m_pCagProxy )
    {
        if( (m_strCagIP != strIP) || (m_uiPort != uiPort) )
        {
            m_strCagIP = strIP;
            m_uiPort = uiPort;
            m_bCagConnected = false;//重新指定主站，因此需要重置和主站的连接状态
            m_pCagProxy->setHostAddress( strIP, uiPort, strCac );
        }
    }
}

/*************************************************
函数名： uploadCACHeartbeatInfo
输入参数： NULL
输出参数： NULL
返回值： 心跳信息处理结果及其他命令信息
功能： 更新心跳信息
*************************************************************/
void CACClient::uploadCACHeartbeatInfo(  )
{
    if( !m_pAccessor->CACId().isEmpty() )
    {
        QSharedPointer<HeartBeatParam> hbParam( new HeartBeatParam );

        //设置智能辅控终端基本信息
        hbParam->setCACID( m_pAccessor->CACId() );
        CACInfo tmpInfo;
        tmpInfo.s_strIP = m_pAccessor->getwebSettingIPAddress();
        tmpInfo.s_dTemperature = m_pAccessor->CACTemperature();
        tmpInfo.s_dateTime = QDateTime::currentDateTime();
        tmpInfo.s_4gSignalLevel = m_pAccessor->CAC4gSignal();
        tmpInfo.s_powerStatus = m_pAccessor->CACPowStatus();
        tmpInfo.s_selfCheckStatus = "true";
        hbParam->setCACInfo( tmpInfo );

        //设置汇聚节点心跳信息
        QVector<SensorHeartbeatInfo> vHbInfos = m_pAccessor->sensorsHeartbeatInfo();
        for( int i = 0; i < vHbInfos.size(); i++ )
        {
            hbParam->addCACInfoHeartbeat( vHbInfos.at(i) );
        }

        for(int i = 0; i < m_setConfigList.size(); i++ )
        {
            hbParam->addSetConfigParam(m_setConfigList.at(i));
        }
        m_setConfigList.clear();
        addOperatorParam( hbParam );
    }
}

/*************************************************
函数名： reponseToCACCommand
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应配置命令（携带操作结果）
*************************************************************/
void CACClient::reponseToCACCommand(const QVector<SetConfigParam> &setConfigParam)
{
    if( !m_pAccessor->CACId().isEmpty() )
    {
        QSharedPointer<HeartBeatParam> hbParam( new HeartBeatParam );

        //设置智能辅控终端基本信息
        hbParam->setCACID( m_pAccessor->CACId() );
        CACInfo tmpInfo;
        tmpInfo.s_strIP = m_pAccessor->CACIpAddress();
        tmpInfo.s_dTemperature = m_pAccessor->CACTemperature();
        tmpInfo.s_dateTime = QDateTime::currentDateTime();
        tmpInfo.s_4gSignalLevel = m_pAccessor->CAC4gSignal();
        tmpInfo.s_powerStatus = m_pAccessor->CACPowStatus();
        hbParam->setCACInfo( tmpInfo );

        //设置汇聚节点心跳信息
        QVector<SensorHeartbeatInfo> vHbInfos = m_pAccessor->sensorsHeartbeatInfo();

        for( int i = 0; i < vHbInfos.size(); i++ )
        {
            hbParam->addCACInfoHeartbeat( vHbInfos.at(i) );
        }

        for(int i = 0; i < setConfigParam.size(); i++ )
        {
            hbParam->addSetConfigParam(setConfigParam.at(i));
        }

        addOperatorParam( hbParam );
    }
}

void CACClient::uploadCommandExecStatus(const CommandInfo &stCommandInfo)
{
    QSharedPointer<UploadCommandExecStatusParam> spUpCommandStatus(new UploadCommandExecStatusParam);
    spUpCommandStatus->setCommandInfo(stCommandInfo);

    addOperatorParam(spUpCommandStatus);
}

CommandInfo CACClient::getCommandExecInfo(QString strCommandSn, QString strCommandObjID, I2CommandType strCommandType,
                                          _CommandInfo::CommandExecRet eComandExecRet, QString strMessage)
{
    CommandInfo stCommandInfo;
    stCommandInfo.s_strSN = strCommandSn;
    stCommandInfo.s_strObjID = strCommandObjID;
    stCommandInfo.s_eType = strCommandType;

    ConfigValue tempConfig;
    tempConfig.s_strName = "result";
    tempConfig.s_strValue = QString::number((int)eComandExecRet);
    stCommandInfo.s_vConfig.append(tempConfig);

    tempConfig.s_strName = "message";
    tempConfig.s_strValue = strMessage;
    stCommandInfo.s_vConfig.append(tempConfig);

    return stCommandInfo;
}

void CACClient::uploadFile(const CommandInfo &stCommandInfo)
{
    QSharedPointer<UploadFileParam> spUpFile(new UploadFileParam);
    spUpFile->setCommandInfo(stCommandInfo);

    addOperatorParam(spUpFile);
}

//void CACClient::uploadAduFirmUpdateStatus(QVariant aduID, bool bUpdateRet, QString strErrorMsg)
//{
//    if(m_updateManager->isUpdateAdu())
//    {
//        m_updateManager->handleAduFirmUpdateStatus(aduID, bUpdateRet, strErrorMsg);
//    }
//}

//void CACClient::uploadAduFirmUpdateEnd()
//{
//    if(m_updateManager->isUpdateAdu())
//    {
//        m_updateManager->handleAduFirmUpdateEnd();
//    }
//}

/*************************************************
 函数名：
 输入参数： dataNodes -- 需要周期上报的数据
 输出参数： NULL
 返回值： NULL
 功能： 添加需要周期上报的数据
 *************************************************************/
void CACClient::addUploadData( const QString &strPointID, const QString &strDataID )
{
    QMutexLocker locker( &m_PeriodDataMutex );

    DataIDInfo idInfo;
    idInfo.s_strPointID = strPointID;
    idInfo.s_strDataID = strDataID;
#if 1
//    if( m_vPeriodBakDatas.contains( idInfo ) )
//    {
//        //如果以前处理过，则不再发送
//        return;
//    }
//    //TODO: 没有清理??
//    m_vPeriodBakDatas.append( idInfo );

    bool bFind = false;
    for( int i = 0; i < m_vPeriodDatas.size(); i++ )
    {
        DataIDInfo tmpId = m_vPeriodDatas.at(i);
        if( (tmpId.s_strPointID == strPointID) &&
                (tmpId.s_strDataID == strDataID ) )
        {
            bFind = true;
            break;
        }
    }

    if( !bFind )
    {
        m_vPeriodDatas.append( idInfo );
    }
#else
    m_vPeriodDatas.append( idInfo );
#endif
}

/*************************************************
函数名： uploadCACData
输入参数： eType -- 数据上传的类型
输出参数： NULL
返回值： NULL
功能： 上送数据
*************************************************************/
void CACClient::uploadCACData(UploadDataType eType , const QVector<DataNode> &nodes, const QString &strSn)
{
    if(!isClientRunning())
    {
        logWarnning("uploadCACData: client is not running: ") << m_strConfigID;
        return;
    }

    if( !m_pAccessor->CACId().isEmpty() )
    {
        QSharedPointer<UploadDataParam> pParam( new UploadDataParam( eType ) );
        pParam->setHostSN( strSn );
        pParam->setCACID( m_pAccessor->CACId() );
        switch( eType )
        {
        case UPLOAD_RESEND:
        {
            //填充需要重发的数据
            if( !strSn.isEmpty() )
            {
                //获取sn对应的召唤指令所发送的数据
                if( m_callDataMap.contains( strSn ) )
                {
                    QVector<DataIDInfo> dataIDs = m_callDataMap.value( strSn );
                    QVector<DataNode> tmpNodes;
                    foreach( DataIDInfo idInfo, dataIDs )
                    {
                        DataNode tmpNode;
                        bool bRet = m_pAccessor->getSensorData( idInfo.s_strPointID, idInfo.s_strDataID, tmpNode );
                        //handleNodePointID( tmpNode );
                        if(bRet)
                        {
                            tmpNodes.append( tmpNode );
                        }
                    }

                    pParam->addData( tmpNodes );
                }
            }
        }
            break;
        case UPLOAD_GETNEWDATA:
        {
            //填充各个测点最新的数据
            fillLatestDatas( pParam );
        }
            break;
        case UPLOAD_PERIOD:
        {
            //填充需要周期上送的数据
            fillPeriodDatas( pParam );
        }
            break;
        case UPLOAD_WARNING:
        {
            for( int iNode = 0; iNode < nodes.size(); iNode++ )
            {
                pParam->addData( nodes.at(iNode) );
            }
        }
            break;
        default:
            break;
        }

        if( !pParam->uploadDatas().isEmpty() )
        {
            addOperatorParam( pParam );
        }
    }
}

//填充周期数据的请求
void CACClient::fillPeriodDatas(QSharedPointer<UploadDataParam> pParam)
{
    //周期数据已经发送完毕，则发送新缓存的数据
    if( m_vSendingDatas.isEmpty() )
    {
        QMutexLocker locker( &m_PeriodDataMutex );
        m_vSendingDatas = m_vPeriodDatas.takeAll().toVector();
        QVector<DataNode> tmpNodes;
        foreach( DataIDInfo idInfo, m_vSendingDatas )
        {
            DataNode tmpNode;
            bool bRet = m_pAccessor->getSensorData( idInfo.s_strPointID, idInfo.s_strDataID, tmpNode );
            //handleNodePointID( tmpNode );
            if(bRet)
            {
                tmpNodes.append( tmpNode );
            }
            else
            {
                PDS_SYS_DEBUG_LOG("Error in get sensor data ");
                //m_vPeriodDatas.append( idInfo );
            }
        }

        if( !tmpNodes.isEmpty() )
        {
            pParam->addData( tmpNodes );
        }
        //        else
        //        {
        //            m_vSendingDatas.clear();
        //        }
        m_vSendingDatas.clear();
    }
}

//设置测点的point_id并过滤未设置point_id的数据
bool CACClient::handleNodePointID( DataNode& dataNode )
{
    bool bRet = false;
    //获取测点的code
    QString strPointCode;
    foreach( DataAttribute stAttr, dataNode.s_vDataAttrs )
    {
        if( stAttr.s_strName == "InnerSensorID" )
        {
            strPointCode = stAttr.s_strValue;
            break;
        }
    }

    //设置测点的point_id
    NodeID nodeId;
    nodeId.s_strObjId = dataNode.s_sNodeInfo.s_sPointInfo.s_strId;
    nodeId.s_strSensorId = strPointCode;

    if( m_pointIdMap.contains( nodeId ) )
    {
        QString strPointId;
        for( int i = 0; i < dataNode.s_vDataAttrs.size(); i++ )
        {
            //查找到测点的point_id并设置成当前客户端缓存的point_id
            if( dataNode.s_vDataAttrs.at( i ).s_strName == "SensorID" )
            {
                strPointId = dataNode.s_vDataAttrs.at( i ).s_strValue;
                QMap<QString, QString> pointMap = m_pointIdMap.value( nodeId );
                dataNode.s_vDataAttrs[i].s_strValue = pointMap.value( "SensorID" );
                if ( pointMap.value("SensorID").isEmpty() )
                {
                    PDS_SYS_WARNING_LOG("error empty sensorid: %s", strPointId.toLatin1().data());
                }
                break;
            }
        }
        bRet = true;
    }

    return bRet;
}

//填充测点最新数据的请求
void CACClient::fillLatestDatas(QSharedPointer<UploadDataParam> pParam)
{
    QList<QString> pointIDList;
    m_pAccessor->getTestPointIDs(pointIDList);
    QVector<DataNode> tmpNodes;
    QVector<DataIDInfo> idInfos;
    foreach( QString strPointID, pointIDList )
    {
        DataNode tmpNode;
        DataIDInfo tmpIDInfo;
        bool bRet = m_pAccessor->getSensorLatestData( strPointID, tmpNode );
        if( bRet )
        {
            tmpNodes.append( tmpNode );
            tmpIDInfo.s_strPointID = strPointID;
            tmpIDInfo.s_strDataID = tmpNode.s_sNodeInfo.s_strDataID;
            idInfos.append( tmpIDInfo );
        }
    }
    pParam->addData( tmpNodes );
    //将召唤数据缓存起来，以便主站的重发指令
    if( (!pParam->hostSN().isEmpty()) && (!idInfos.isEmpty()) )
    {
        m_callDataMap.clear();//TODO 目前只保存最近一次的召唤
        m_callDataMap[pParam->hostSN()] = idInfos;
    }
}

//更新数据的时间
void CACClient::updatePointEndTime( const QString & strPointId,
                                    const QVector<DataNode> & vDatas )
{
    QString currentTime = m_pointEndTime.value( strPointId );
    for( int i = 0; i < vDatas.size(); i++ )
    {
        if( currentTime < vDatas.at(i).s_sNodeInfo.s_strSampleTime )
        {
            QDateTime timeData = QDateTime::fromString(vDatas.at(i).s_sNodeInfo.s_strSampleTime,
                                                       "yyyy-MM-dd hh:mm:ss");
            currentTime = timeData.addSecs(1).toString("yyyy-MM-dd hh:mm:ss");
        }
    }
    if (!currentTime.isEmpty())
    {
        m_pointEndTime[strPointId] = currentTime;
    }
}

/*************************************************
函数名： uploadCACConfig
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 上送配置信息
*************************************************************/
void CACClient::uploadCACConfig(const QString &strSn)
{
    if( !m_pAccessor->CACId().isEmpty() )
    {
        QSharedPointer<UploadConfigParam> pParam( new UploadConfigParam );
        pParam->setCACID( m_pAccessor->CACId() );
        pParam->setHostSN( strSn );

        QVector<ConfigParam> vParams;
        m_pAccessor->getConfigs( CONFIG_PARAM, vParams );
        for( int i = 0; i < vParams.size(); i++ )
        {
            pParam->addConfigParam( vParams.at(i) );
        }

        vParams.clear();
        m_pAccessor->getConfigs( DATA_PARAM, vParams );
        for( int i = 0; i < vParams.size(); i++ )
        {
            pParam->addDataParam( vParams.at(i) );
        }

        if( !isConfigInQueue() )
        {
            addOperatorParam( pParam );
        }
        else
        {
            m_configBuf.append( pParam );
        }
    }
    PDS_SYS_DEBUG_LOG("--uploadCACConfig: %s" , strSn.toLatin1().data());
}

/*************************************************
函数名： downloadCACLatestVersion
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 获取最新文件版本
*************************************************************/
void CACClient::downloadCACLatestVersion( const NodeID& nodeId )
{
    if( !m_pAccessor->CACId().isEmpty() )
    {
        QSharedPointer<DownloadLatestVersionParam> latestVerParam( new DownloadLatestVersionParam );

        //设置智能辅控终端基本信息
        latestVerParam->setCACID( m_pAccessor->CACId() );

        //设置节点信息
        latestVerParam->setNodeID( nodeId );

        addOperatorParam( latestVerParam );
    }
}

/*************************************************
函数名： downloadCACHistoryVersion
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 下载历史文件信息
*************************************************************/
void CACClient::downloadCACHistoryVersion(const NodeID &nodeId)
{
    if( !m_pAccessor->CACId().isEmpty() )
    {
        QSharedPointer<DownloadHistoryVersionParam> latestVerParam( new DownloadHistoryVersionParam );

        //设置智能辅控终端基本信息
        latestVerParam->setCACID( m_pAccessor->CACId() );

        //设置节点信息
        latestVerParam->setNodeID( nodeId );

        addOperatorParam( latestVerParam );
    }
}

/*************************************************
函数名： downloadCACUpdateFile
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 下载文件
*************************************************************/
void CACClient::downloadCACUpdateFile(const I2VersionInfo &verInfo )
{
    if( !m_pAccessor->CACId().isEmpty() )
    {
        QSharedPointer<DownloadFileParam> pParam( new DownloadFileParam );
        pParam->setCACID( m_pAccessor->CACId() );

        I2VersionInfo tmpInfo = verInfo;
        if(VersionManager::instance()->getInstallFileInfo( tmpInfo.s_sFileIDInfo, tmpInfo.s_sFileInfo ))
        {
            //从本地重新获取版本对应信息
            pParam->setVersionInfo( tmpInfo );

            addOperatorParam( pParam );
        }
    }
}

bool CACClient::isConfigInQueue()
{
    bool bFind = false;
    m_queueMutex.lock();

    foreach( QSharedPointer<ParamBase> pParam, m_vOperateQueue )
    {
        if( pParam->operateType() == UPLOAD_CONFIG )
        {
            bFind = true;
            break;
        }
    }

    m_queueMutex.unlock();

    return bFind;
}

/*************************************************
函数名： isSlaveClientRun
输入参数： NULL
输出参数： NULL
返回值：true/false
功能：本客户端是否运行
*************************************************************/
bool CACClient::isClientRunning( ) const
{
    return m_bRun;
}

#ifdef I2_IED_MODE
void CACClient::onReportData(const PDSMPDATA &data)
{
    DataReport::instance().reportData(data);
}
#endif

void CACClient::clearClient()
{
   m_vOperateQueue.clear();
   m_pointInfoMap.clear();
   m_pointEndTime.clear();
   m_pointIdMap.clear();
   m_vPeriodDatas.clear();
   m_vPeriodBakDatas.clear();
   m_vSendingDatas.clear();
   m_vWarningDataNodes.clear();
   m_callDataMap.clear();
   m_configBuf.clear();
   m_setConfigList.clear();
   m_updateFailMap.clear();
   m_commandExecStatusBuf.clear();
   m_strCagIP.clear();
}

void CACClient::clearCacheDB()
{
    CacheDataBase::instance()->delI2FailDataFromCacheDB(m_strCagIP, m_uiPort);
}


//CACUpdateManager::CACUpdateManager(CACClient * const pClient, QObject *parent):QObject(parent),
//    m_cacClient(pClient),
//    m_OTATaskFilePath(QLatin1String("/media/data/hostupdate")),
//    m_OTATaskFileName(QLatin1String("otaTaskDoc"))
//{
//    m_bIsExecCommand = false;

//    //加载任务文件内容到任务列表
//    loadOTATaskFromFile();

//    //检查主机升级结果
//    checkCacUpdateRet();

//    //当前没有在执行命令
//    if(!m_bIsExecCommand)
//    {
//        handleOTACommand();
//    }
//}

CACUpdateManager &CACUpdateManager::instance()
{
    static CACUpdateManager instance;
    return instance;
}

void CACUpdateManager::addOTACommand(const QList<CommandInfo> &commandList)
{
    m_cacUpdateMutex.lock();
    m_OTACommandList.append(commandList);
    m_cacUpdateMutex.unlock();

    saveOTATaskToFile();
    logInfo(QString("CACUpdateManager::addOTACommand otaCommand list size: %1, info is: %2.").arg(m_OTACommandList.size()).arg(getAllCommandInfo()));

    if(!m_bIsExecCommand)
    {
       handleOTACommand();
    }
}
void CACUpdateManager::delOTACommand(QString strCommandSN)
{
    for(auto iter = m_OTACommandList.begin(); iter != m_OTACommandList.end(); ++iter)
    {
        if(iter->s_strSN == strCommandSN)
        {
            m_OTACommandList.erase(iter);
            break;
        }
    }
}

void CACUpdateManager::removeFirstCommand(QString strCommandSN)
{
    if(!m_OTACommandList.isEmpty())
    {
        if(m_OTACommandList.first().s_strSN == strCommandSN)
        {
            m_OTACommandList.removeFirst();
        }
    }
    saveOTATaskToFile();
}

QString CACUpdateManager::getAllCommandInfo()
{
    QStringList strAllCommandInfo;
    foreach (const CommandInfo& tmpCommand, m_OTACommandList)
    {
        QString strCommand = QString("CommandSN:%1,CommandObjID:%2,CommandType:%3").arg(tmpCommand.s_strSN).arg(tmpCommand.s_strObjID)
                .arg(ParamBase::converTypeEnumToString(tmpCommand.s_eType));
        strAllCommandInfo.append(strCommand);
    }
    return strAllCommandInfo.join(';');
}

void CACUpdateManager::doUpdateTask(const I2VersionInfo &verInfo)
{
    logInfo("CACUpdateManager::doUpdateTask...");

    if(verInfo.s_sFileIDInfo.s_strCommandSn != m_currentUpdateInfo.m_currentHandleCommand.s_strSN)
    {
        logError("---doUpdateTask CommandSn different");
        return;
    }
    //01 md5校验
    if(!VersionManager::instance()->fileCheck(verInfo.s_sFileIDInfo))//md5校验失败
    {
        logError("VersionManager::fileCheck MD5Value No Match");

        //处理更新错误
        handleUpdateError(verInfo, UpdateErrorCode::FILE_CHECK_FAILED);
    }
    else//md5校验成功
    {
        logInfo("VersionManager::fileCheck successful");

        if(verInfo.s_bAutoInstall)
        {
            //主机固件更新  主机自动安装升级包则保存升级命令信息
            if(verInfo.s_sFileIDInfo.s_strSensorId.isEmpty())
            {
                if(VersionManager::instance()->saveAutoInstallInfo(verInfo.s_sFileIDInfo, m_cacClient->m_strCagIP))
                {
                    //上送固件更新执行中状态
                    uploadTaskExecStatus(verInfo, _CommandInfo::COMMAND_EXEC_RUNNING, "");

                    if(!VersionManager::instance()->installUpdateFile( verInfo.s_sFileIDInfo.s_strVersion ))
                    {
                        //上送固件更新失败状态
                        //uploadTaskExecStatus(verInfo, _CommandInfo::COMMAND_EXEC_FAILED, "install failed");
                        handleUpdateError(verInfo, UpdateErrorCode::INSTALL_TIMEOUT);
                    }
                }
                else
                {
                    logError("VersionManager::saveAutoInstallInfo failed");

                    //处理更新错误
                    handleUpdateError(verInfo, UpdateErrorCode::SAVE_INSTALL_INFO_FAILED);
                }
            }
            else if(!verInfo.s_sFileIDInfo.s_strSensorId.isEmpty()) //传感器固件更新
            {
                QSharedPointer<HostAccessor> spHostAccessor = qSharedPointerDynamicCast<HostAccessor>(m_cacClient->m_pAccessor);
                if(!spHostAccessor.isNull())
                {
                    QStringList updateAduList(verInfo.s_sFileIDInfo.s_strSensorId.split(','));
                    QString updateFilePath = VersionManager::instance()->getUpdateFileDirInfo(verInfo.s_sFileIDInfo);

                    //保存传感器更新信息，给传感器返回更新状态使用
                    m_currentUpdateInfo.m_currentAduUpdateInfo.clear();
                    m_currentUpdateInfo.m_currentAduUpdateInfo.needUpdateAduList = updateAduList;

                    spHostAccessor->firmUpdateForAdu(updateAduList, verInfo.s_sFileInfo.s_strFileName, updateFilePath);
                    logInfo(QString("update adu:%1, firmwareFilepath:%2").arg(updateAduList.join(',')).arg(updateFilePath));
                }
            }
        }
        else
        {
            //不自动安装,当前更新任务结束,执行下个任务
            OTACommandExecEnd();
        }
    }
}

void CACUpdateManager::handleUpdateError(const I2VersionInfo &verInfo, UpdateErrorCode eErrorCode)
{
    logInfo("CACUpdateManager::handleUpdateError...");

    if(verInfo.s_sFileIDInfo.s_strCommandSn != m_currentUpdateInfo.m_currentHandleCommand.s_strSN)
    {
        logError("---current Handle CommandSn different");
        return;
    }

    //上送任务失败消息
    uploadTaskExecStatus(verInfo, _CommandInfo::COMMAND_EXEC_FAILED, QString::number((quint16)eErrorCode));

    //任务执行异常停止
    //OTACommandExecAbort();
    OTACommandExecEnd();
}

void CACUpdateManager::handleTaskExpired(const I2VersionInfo &verInfo)
{
    logInfo("CACUpdateManager::handleTaskExpired...");

    if(verInfo.s_sFileIDInfo.s_strCommandSn != m_currentUpdateInfo.m_currentHandleCommand.s_strSN)
    {
        logError("---current Handle CommandSn different");
        return;
    }

    //任务执行结束
    OTACommandExecEnd();
}

void CACUpdateManager::startUpdateTask()
{
    if(!m_bIsExecCommand)
    {
        handleOTACommand();
    }
}

bool CACUpdateManager::isUpdateAdu()
{
    return m_bIsExecCommand && (m_currentUpdateInfo.m_currentUpdateType == SENSOR_DEVICE);
}

void CACUpdateManager::handleAduFirmUpdateStatus(QVariant aduID, bool bUpdateRet, QString strErrorMsg)
{
    QString updateAduID = aduID.toString();
    static QMap<QString, int> ErrorMsgMap
    {
        {"get link failed", 1},
        {"wakeup Adu failed", 2},
        {"connect Adu failed", 3},
        {"get Adu boot version failed", 4},
        {"Adu entering boot mode failed", 5},
        {"Adu not in boot mode", 6},
        {"erase Adu flash failed", 7},
        {"write firmware failed", 8},
        {"Adu verify firmware failed", 9},
        {"Adu update firmware finish failed", 10},
        {"deliver firmware overview failed", 11},
        {"Adu execute update failed", 12},
        {"query Adu omitted block failed", 13},
        {"rewrite omitted block failed", 14},
        {"get Adu selfCheck result failed", 15},
        {"get Adu version info failed", 16},
        {"Adu version check error", 17}
    };
    logInfo(QString("handleAduFirmUpdateStatus strErrorMsg:%1,enumValue:%2").arg(strErrorMsg).arg(ErrorMsgMap.value(strErrorMsg)));

    if(m_currentUpdateInfo.m_currentAduUpdateInfo.needUpdateAduList.contains(updateAduID))
    {
        CommandInfo stCommandInfo = m_currentUpdateInfo.m_currentHandleCommand;
        stCommandInfo.s_vConfig.clear();

        ConfigValue tempConfig;
        tempConfig.s_strName = "result";
        tempConfig.s_strValue = QString::number((int)_CommandInfo::COMMAND_EXEC_RUNNING);
        stCommandInfo.s_vConfig.append(tempConfig);

        tempConfig.s_strName = "message";
        tempConfig.s_strValue = QString::number(ErrorMsgMap.value(strErrorMsg));
        stCommandInfo.s_vConfig.append(tempConfig);

        tempConfig.s_strName = "id";
        tempConfig.s_strValue = updateAduID;
        stCommandInfo.s_vConfig.append(tempConfig);

        tempConfig.s_strName = "isSucc";
        if(bUpdateRet)
        {
            tempConfig.s_strValue = "0";
            m_currentUpdateInfo.m_currentAduUpdateInfo.updateSuccessfulAduList.append(updateAduID);
        }
        else
        {
            tempConfig.s_strValue = "1";
        }
        stCommandInfo.s_vConfig.append(tempConfig);

        if(!m_cacClient.isNull() && m_cacClient->isClientRunning())
        {
            m_cacClient->uploadCommandExecStatus(stCommandInfo);
        }
    }
}

void CACUpdateManager::handleAduFirmUpdateEnd()
{
    CommandInfo stCommandInfo = m_currentUpdateInfo.m_currentHandleCommand;
     stCommandInfo.s_vConfig.clear();

    _CommandInfo::CommandExecRet eCommandExecRet;
    QStringList strFailedIDList;
    if(m_currentUpdateInfo.m_currentAduUpdateInfo.getUpdateAduRet())
    {
        eCommandExecRet = _CommandInfo::COMMAND_EXEC_SUCCESSFUL;
    }
    else
    {
        eCommandExecRet = _CommandInfo::COMMAND_EXEC_FAILED;
        m_currentUpdateInfo.m_currentAduUpdateInfo.getFailedID(strFailedIDList);
    }
    ConfigValue tempConfig;
    tempConfig.s_strName = "result";
    tempConfig.s_strValue = QString::number((int)eCommandExecRet);
    stCommandInfo.s_vConfig.append(tempConfig);

    tempConfig.s_strName = "message";
    tempConfig.s_strValue = "";
    stCommandInfo.s_vConfig.append(tempConfig);

    tempConfig.s_strName = "failureids";
    tempConfig.s_strValue = strFailedIDList.join(',');
    stCommandInfo.s_vConfig.append(tempConfig);

    if(!m_cacClient.isNull() && m_cacClient->isClientRunning())
    {
        m_cacClient->uploadCommandExecStatus(stCommandInfo);
    }

    //命令执行结束
    OTACommandExecEnd();
}

void CACUpdateManager::handleOTACommand()
{
    logInfo(QString("CACUpdateManager::handleOTACommand otaCommand list size: %1, info is: %2.").arg(m_OTACommandList.size()).arg(getAllCommandInfo()));

    if(!m_OTACommandList.isEmpty())
    {
        m_bIsExecCommand = true;

        const CommandInfo &stCommandInfo = m_OTACommandList.first();
        m_currentUpdateInfo.m_currentHandleCommand =  stCommandInfo;
        m_cacClient = HSTCommunication::instance()->getStationClient(stCommandInfo.strStationIP);

        if(stCommandInfo.s_strObjID == ConfigService::instance().getIedRtuID())
        {
            //
            QMap<QString, QString> qmap4ActionParameter;
            for(QVector<ConfigValue>::const_iterator iterAction = stCommandInfo.s_vConfig.cbegin();
                iterAction != stCommandInfo.s_vConfig.cend(); ++iterAction)
            {
                qmap4ActionParameter.insert(iterAction->s_strName, iterAction->s_strValue);
            }

            I2VersionInfo stUpdateInfo;
            stUpdateInfo.s_sFileIDInfo.s_strCommandSn = stCommandInfo.s_strSN;

            stUpdateInfo.s_sFileIDInfo.s_strObjId = qmap4ActionParameter.value(OBJID);
            stUpdateInfo.s_sFileIDInfo.s_strSensorId = qmap4ActionParameter.value(QStringLiteral("aduIds"));
            stUpdateInfo.s_sFileIDInfo.s_strVersion = qmap4ActionParameter.value(VERSION_NO);
            QString strFileType = qmap4ActionParameter.value(QStringLiteral("fileType"));
            if(strFileType == QStringLiteral("FW"))
            {
                stUpdateInfo.s_sFileIDInfo.s_eType = UpdateFileNS::FIRMWARE_TYPE;
            }
            stUpdateInfo.s_sFileIDInfo.s_strSignatureMethod = qmap4ActionParameter.value(QStringLiteral("signatureMethod"));
            stUpdateInfo.s_sFileIDInfo.s_strSignatureValue = qmap4ActionParameter.value(QStringLiteral("signatureValue"));
            stUpdateInfo.s_sFileIDInfo.s_strUrl = qmap4ActionParameter.value(QStringLiteral("url"));

            stUpdateInfo.s_sFileInfo.s_strReleaseTime = qmap4ActionParameter.value(PUBLISH_TIME);
            stUpdateInfo.s_sFileInfo.s_strFileName = qmap4ActionParameter.value(FILE_NAME);
            stUpdateInfo.s_sFileInfo.s_uiTotalSize = qmap4ActionParameter.value(FILE_SIZE).toUInt();
            uint uiAuto = qmap4ActionParameter.value(AUTO_INSTALL).toUInt();
            stUpdateInfo.s_bAutoInstall = uiAuto > 0;

            //确定更新类型
            m_currentUpdateInfo.m_currentUpdateType = CAC_DEVICE;
            if(!stUpdateInfo.s_sFileIDInfo.s_strSensorId.isEmpty())
            {
                m_currentUpdateInfo.m_currentUpdateType = SENSOR_DEVICE;
            }
            else
            {
                //待升级版本与主机版本相同,停止升级
                const QString strCurFirmwareVersion = ConfigService::instance().getFirmwareVersion();
                if(strCurFirmwareVersion == stUpdateInfo.s_sFileIDInfo.s_strVersion)
                {
                    handleUpdateError(stUpdateInfo, UpdateErrorCode::SAME_VERSION);
                    logError("same host Firmware version,stop update...");
                    return;
                }
            }

            //清理旧的安装包信息
            VersionManager* const pVersionInfoManager  =   VersionManager::instance();
            pVersionInfoManager->deleteUpdateFile({stUpdateInfo.s_sFileIDInfo});

            //将更新文件信息保存到本地
            pVersionInfoManager->updateVersionInfo( stUpdateInfo.s_sFileIDInfo,
                                                           stUpdateInfo.s_sFileInfo.s_strFileName,
                                                           stUpdateInfo.s_sFileInfo.s_uiTotalSize,
                                                           stUpdateInfo.s_sFileInfo.s_strReleaseTime );
            pVersionInfoManager->setAuxCtrlID(stCommandInfo.s_strObjID);

            //下载固件
            if(!m_cacClient.isNull() && m_cacClient->isClientRunning())
            {
                m_cacClient->downloadCACUpdateFile(stUpdateInfo);
            }
        }
        else
        {
            logError(QString("Command ObjectID is %1, IedRtuID is %2").
                     arg(stCommandInfo.s_strObjID).
                     arg(ConfigService::instance().getIedRtuID())
                     );

            //上送命令执行失败状态
            CommandInfo CommandExecInfo = CACClient::getCommandExecInfo(stCommandInfo.s_strSN, stCommandInfo.s_strObjID, stCommandInfo.s_eType,
                                                             _CommandInfo::COMMAND_EXEC_FAILED, QStringLiteral("objid error"));

            ConfigValue tempConfig{"failureids", stCommandInfo.s_strObjID};
            CommandExecInfo.s_vConfig.append(tempConfig);

            if(!m_cacClient.isNull() && m_cacClient->isClientRunning())
            {
                m_cacClient->uploadCommandExecStatus(CommandExecInfo);
            }
        }

   }
    else
    {
        m_bIsExecCommand = false;
        m_currentUpdateInfo.clear();
        m_cacClient.clear();
    }
}

void CACUpdateManager::OTACommandExecEnd()
{
    //更新结束
    m_bIsExecCommand = false;

    //从OTA命令列表中删除当前命令
    removeFirstCommand(m_currentUpdateInfo.m_currentHandleCommand.s_strSN);

    //清空当前更新信息
    m_currentUpdateInfo.clear();

    //清除任务所属客户端信息
    m_cacClient.clear();

    //执行下一个OTA命令
    handleOTACommand();
}

void CACUpdateManager::OTACommandExecAbort()
{
    //更新结束
    m_bIsExecCommand = false;

    //从OTA命令列表中删除当前命令
    removeFirstCommand(m_currentUpdateInfo.m_currentHandleCommand.s_strSN);

    //清空当前更新信息
    m_currentUpdateInfo.clear();
}

void CACUpdateManager::loadOTATaskFromFile()
{
    QFileInfo taskFileInfo(m_OTATaskFilePath, m_OTATaskFileName);
    if(!taskFileInfo.exists())
    {
        return;
    }

    QFile taskFile(taskFileInfo.absoluteFilePath());
    if(!taskFile.open(QIODevice::ReadOnly))
    {
        return;
    }

    QDomDocument OTATaskDoc;
    if (OTATaskDoc.setContent(&taskFile))
    {
        QDomElement commands = OTATaskDoc.documentElement();

        for( QDomElement cmdElem = commands.firstChildElement( COMMAND );
             !cmdElem.isNull(); cmdElem = cmdElem.nextSiblingElement( COMMAND ) )
        {
            CommandInfo tmpCommand;
            // 解析属性结点
            tmpCommand.s_strObjID = cmdElem.attribute( OBJID );
            tmpCommand.s_eType = ParamBase::coverStringTypeToEnum( cmdElem.attribute( COMMAND_TYPE ) );
            tmpCommand.s_strSN = cmdElem.attribute( COMMAND_SN );
            tmpCommand.strStationIP = cmdElem.attribute( STATION_IP );

            // 解析参数列表
            QDomElement actionElem = cmdElem.firstChildElement( ACTION );
            while( !actionElem.isNull() )
            {
                ConfigValue tmpAction;
                // 参数名属性
                tmpAction.s_strName = actionElem.attribute( PARAM_NAME );
                // 参数值属性
                tmpAction.s_strValue = actionElem.attribute( PARAM_VALUE );

                tmpCommand.s_vConfig.push_back( tmpAction );

                // 获取下一个参数结点
                actionElem = actionElem.nextSiblingElement( ACTION );
            }

            //任务信息保存到待处理列表
            m_OTACommandList.append( tmpCommand );
        }

    }
    taskFile.close();
}

void CACUpdateManager::saveOTATaskToFile()
{
    QDomDocument OTATaskDoc;
    // 根结点
    QDomElement commands = OTATaskDoc.createElement( COMMANDS );
    foreach (const CommandInfo& tmpInfo, m_OTACommandList)
    {
        // Command 结点
        QDomElement command = OTATaskDoc.createElement( COMMAND );
        command.setAttribute( COMMAND_SN, tmpInfo.s_strSN );
        command.setAttribute( OBJID, tmpInfo.s_strObjID );
        command.setAttribute( COMMAND_TYPE, ParamBase::converTypeEnumToString(tmpInfo.s_eType));
        command.setAttribute( STATION_IP, tmpInfo.strStationIP);

        // action 结点
        for(auto configIter = tmpInfo.s_vConfig.cbegin(); configIter != tmpInfo.s_vConfig.cend(); ++configIter)
        {
            QDomElement action = OTATaskDoc.createElement( ACTION );
            action.setAttribute(PARAM_NAME, configIter->s_strName);
            action.setAttribute(PARAM_VALUE, configIter->s_strValue);

            command.appendChild(action);
        }

        commands.appendChild(command);
    }

    OTATaskDoc.appendChild(commands);

    //保存到文件
    QDir fileDir(m_OTATaskFilePath);
    if(!fileDir.exists())
    {
        fileDir.mkpath(m_OTATaskFilePath);
    }
    QFileInfo taskFileInfo(m_OTATaskFilePath, m_OTATaskFileName);
    QFile taskFile(taskFileInfo.absoluteFilePath());

    if(taskFile.open(QIODevice::WriteOnly | QIODevice::Truncate))
    {
        taskFile.write(OTATaskDoc.toByteArray());
        taskFile.close();
    }
}

void CACUpdateManager::checkCacUpdateRet()
{
    logInfo("CACUpdateManager::checkCacUpdateRet");

    //01 查看升级文件是否存在
    const QString UPGRADE_FILE_PATH("/media/data/upgrade");
    QDir installDir( UPGRADE_FILE_PATH );
    QFileInfo installRecordFileInfo(installDir, "AutoInstallRecord.ini");
    if(!installRecordFileInfo.exists())
    {
        return;
    }

    //02 读取升级文件信息
    QSettings recordSettings(installRecordFileInfo.absoluteFilePath(), QSettings::IniFormat);

    recordSettings.beginGroup("AutoInstallRecord");
    QString strCommandSn = recordSettings.value("CommandSn").toString();
    QString strObjId = recordSettings.value("ObjId").toString();
    //QString strCommandType = recordSettings.value("CommandType").toString();
    QString strUpdateVersion = recordSettings.value("UpdateVersion").toString();
    QString strStationIP = recordSettings.value("StationIP").toString();
    recordSettings.endGroup();

    //恢复升级状态
    m_bIsExecCommand = true;
    m_currentUpdateInfo.m_currentUpdateType = CAC_DEVICE;
    m_currentUpdateInfo.m_currentHandleCommand.s_strSN = strCommandSn;
    m_currentUpdateInfo.m_currentHandleCommand.s_eType = CMD_TYPE_OTA;
    m_currentUpdateInfo.m_currentHandleCommand.s_strObjID = strObjId;
    m_cacClient = HSTCommunication::instance()->getStationClient(strStationIP);


    //03 准备命令执行结果信息
    _CommandInfo::CommandExecRet otaCommandExecRet;  //命令执行结果信息
    const QString strCurFirmwareVersion = ConfigService::instance().getFirmwareVersion();
    if(strUpdateVersion == strCurFirmwareVersion)
    {
        otaCommandExecRet = _CommandInfo::COMMAND_EXEC_SUCCESSFUL;
    }
    else
    {
        otaCommandExecRet = _CommandInfo::COMMAND_EXEC_FAILED;
    }

    CommandInfo stCommandInfo{
                                strCommandSn, strObjId, _I2CommandType::CMD_TYPE_OTA, "",
                             {
                                {"result", QString::number((int)otaCommandExecRet)},
                                {"message", " "}
                             }
                             };
    if(_CommandInfo::COMMAND_EXEC_FAILED == otaCommandExecRet)
    {
        ConfigValue failureConfig{"failureids", strObjId};
        stCommandInfo.s_vConfig.append(failureConfig);
    }

    logInfo(QString("UpdateVersion is %1, Current practical FirmwareVersion is %2").arg(strUpdateVersion).arg(strCurFirmwareVersion));

    //04 上送执行结果信息
    if(!m_cacClient.isNull() && m_cacClient->isClientRunning())
    {
        m_cacClient->uploadCommandExecStatus(stCommandInfo);

        //05 移除升级记录文件
        QFile::remove(installRecordFileInfo.absoluteFilePath());
    }

    //主机升级结束
    OTACommandExecEnd();
}

void CACUpdateManager::uploadTaskExecStatus(const I2VersionInfo &verInfo, _CommandInfo::CommandExecRet eComandExecRet, QString strMessage)
{
    CommandInfo CommandExecInfo = CACClient::getCommandExecInfo(verInfo.s_sFileIDInfo.s_strCommandSn,
                                                                verInfo.s_sFileIDInfo.s_strObjId, CMD_TYPE_OTA,
                                                                eComandExecRet, strMessage);
    if(eComandExecRet == _CommandInfo::COMMAND_EXEC_FAILED)
    {
        ConfigValue configValue{"failureids", ""};
        if(verInfo.s_sFileIDInfo.s_strSensorId.isEmpty())
        {
            configValue.s_strValue = verInfo.s_sFileIDInfo.s_strObjId;
        }
        else
        {
            configValue.s_strValue = verInfo.s_sFileIDInfo.s_strSensorId;
        }
        CommandExecInfo.s_vConfig.append(configValue);
    }

    if(!m_cacClient.isNull() && m_cacClient->isClientRunning())
    {
        m_cacClient->uploadCommandExecStatus(CommandExecInfo);
    }
}

CACUpdateManager::CACUpdateManager(QObject *parent):QObject(parent),
    m_OTATaskFilePath(QLatin1String("/media/data/hostupdate")),
    m_OTATaskFileName(QLatin1String("otaTaskDoc"))
{
    m_bIsExecCommand = false;

    //加载任务文件内容到任务列表
    loadOTATaskFromFile();

    //检查主机升级结果
    checkCacUpdateRet();

    //当前没有在执行命令
    if(!m_bIsExecCommand)
    {
        handleOTACommand();
    }
}



