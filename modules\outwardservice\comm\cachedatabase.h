﻿/*
* Copyright (c) 2018.1，南京华乘电气科技有限公司
* All rights reserved.
*
*
*
* 初始版本：1.0.0.0
* 作者：
* 创建日期：
* 摘要：上送数据缓存

* 当前版本：1.0.0.0
*/
#ifndef CACHEDATABASE_H
#define CACHEDATABASE_H

#include <QObject>

#include <QSqlDatabase>
#include <QDateTime>
#include <QTimer>

enum OutwardProtocolType
{
    UNKNOWN = 0,
    IEC104,
    I2
};
struct DataRecordInfo
{
    QString strTestPointId;
    QDateTime qdtRecordTime;
};

class QThread;
typedef struct _DataNode DataNode;
typedef struct _PDSMPDATA PDSMPDATA;
class CacheDataBase : public QObject
{
    Q_OBJECT
public:

    /************************************************
     * 功能：单例
     ************************************************/
    static CacheDataBase* instance();

    //获取I2上送失败数据
    bool getI2FailData(QList<PDSMPDATA> &failDataList, QString strCagIP, quint32 uiPort);

    //获取I2上送失败数据信息列表
    QList<DataRecordInfo> getI2FailDataInfo(QString strCagIP, quint32 uiPort);

    /** @brief 获取I2上送失败数据信息列表（分页版本）
     * @param strCagIP CAG IP地址
     * @param uiPort 端口号
     * @param pageSize 页大小
     * @param pageIndex 页索引
     * @return 失败数据信息列表
     */
    QList<DataRecordInfo> getI2FailDataInfoPaged(QString strCagIP, quint32 uiPort, int pageSize = 1000, int pageIndex = 0);

    /** @brief 获取I2上送失败数据的总数量
     * @param strCagIP CAG IP地址
     * @param uiPort 端口号
     * @return 失败数据的总数量
     */
    int getI2FailDataCount(QString strCagIP, quint32 uiPort);

    //清理过期的I2失败数据（超过指定天数）
    bool cleanupOldI2FailData(QString strCagIP, quint32 uiPort, int daysToKeep = 7);

    //清理过多的I2失败数据（保留最新的N条记录）
    bool cleanupExcessI2FailData(QString strCagIP, quint32 uiPort, int maxRecords = 10000);

    //删除指定I2主站失败缓存数据
    bool delI2FailDataFromCacheDB(QString strCagIP, quint32 uiPort);

    //将I2上送记录保存到数据库
    bool addI2DataToCacheDB(QString strCagIP, quint32 uiPort, const QVector<DataNode>& dataVector);

    //从数据库清除I2上送记录
    bool delI2DataFromCacheDB(QString strCagIP, quint32 uiPort, const QVector<DataNode>& dataVector);

    /*************************************************
    功能： 104缓存数据库初始化
    输入参数：
            ipAddress:  客户端ip
    *************************************************************/
    void initIec104CacheDB(QString strIPAddress);

    //将104上送记录保存到数据库
    bool add104DataToCacheDB(QString strTestPointId, QString strRecordTime);

    //从数据库中删除指定104连接数据记录
    bool del104DataFromCacheDB(QString strTestPointId, QString strRecordTime, QString strIP);


    /*************************************************
    功能： 数据写入缓存库
    输入参数：
            strPointID : 测点ID
            Recordtime： 数据采样时间

    输出参数：
    返回值：
            true -- 成功
            false -- 失败
    *************************************************************/
    bool addRecordToCacheDB(const QString &table,
                            const QString &strPointID,
                            const QString &daqtime);

    /*************************************************
    功能： 数据从缓存库删除
    输入参数：
            strPointID :测点ID
            Recordtime：数据采样时间

    输出参数：
    返回值：
            true -- 成功
            false -- 失败
    *************************************************************/
    bool delRecordToCacheDB(const QString &table,
                            const QString &strPointID,
                            const QString &daqtime);

    /*************************************************
    功能： 数据从缓存库查询是否存在这条记录
    输入参数：
            strPointID :-- 测点ID
            Recordtime：--时间

    输出参数：
    返回值：
            true -- 存在
            false -- 不存在
    *************************************************************/
    bool assertIfExistInCacheDB(const QString &table,
                                const QString &strPointID,
                                const QString &daqtime);
signals:
    //创建104数据表
    void sigCreate104DataTable(QString strIPAddress);

    //添加104数据到缓存数据库
    void sigAdd104Data(QString strTestPointId, QString strRecordTime);

    //从缓存数据库删除104数据
    void sigDel104Data(QString strTestPointId, QString strRecordTime, QString strIP);

    //添加I2数据到缓存数据库
    void sigAddI2Data(QString strCagIP, quint32 uiPort, const QVector<DataNode>& dataVector);

    //从缓存数据库删除I2数据
    void sigDelI2Data(QString strCagIP, quint32 uiPort, const QVector<DataNode>& dataVector);

    //执行定期清理
    void performPeriodicCleanup();

private slots:
    //创建104数据表
    void OnCreate104DataTable(QString strIPAddress);

    //将104上送记录保存到数据库
    void OnAdd104Data(QString strTestPointId, QString strRecordTime);

    //从数据库中删除指定104连接数据记录
    void OnDel104Data(QString strTestPointId, QString strRecordTime, QString strIP);

    //将I2上送记录保存到数据库
    void OnAddI2Data(QString strCagIP, quint32 uiPort, const QVector<DataNode>& dataVector);

    //从数据库清除I2上送记录
    void OnDelI2Data(QString strCagIP, quint32 uiPort, const QVector<DataNode>& dataVector);


private:
    /************************************************
     * 功能：构造函数
     ************************************************/
    explicit CacheDataBase(QObject *parent = 0);

    ~CacheDataBase();

    //初始化缓存数据库
    bool initCacheDB();

    //I2缓存数据库初始化
    void initI2CacheDB();

    //根据接出协议类型获取失败数据
    bool getFailData(OutwardProtocolType eType,QMap<QString, QList<PDSMPDATA> >& failDataMap);

private:
    QThread * m_pThread;                        //运行线程
    QSqlDatabase m_cacheSqliteDB;               //缓存数据库
    QTimer * m_pCleanupTimer;                   //定期清理定时器
};

#endif // CACHEDATABASE_H
